{"name": "vuryss/doctrine-lazy-json-odm", "description": "A high-performance Doctrine JSON ODM with lazy loading using vuryss/serializer v2", "type": "symfony-bundle", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.4", "doctrine/dbal": "^4.0", "doctrine/orm": "^3.4", "symfony/framework-bundle": "^7.0", "symfony/dependency-injection": "^7.0", "symfony/config": "^7.0", "vuryss/serializer": "^2.0", "halaxa/json-machine": "^1.2"}, "require-dev": {"pestphp/pest": "*", "phpstan/phpstan": "*", "captainhook/captainhook": "*", "friendsofphp/php-cs-fixer": "*", "captainhook/hook-installer": "*", "ramsey/conventional-commits": "*", "symfony/phpunit-bridge": "^7.0", "mockery/mockery": "^1.6"}, "config": {"allow-plugins": {"captainhook/hook-installer": true, "pestphp/pest-plugin": true}}, "autoload": {"psr-4": {"Vuryss\\DoctrineLazyJsonOdm\\": "src/"}}, "autoload-dev": {"psr-4": {"Vuryss\\DoctrineLazyJsonOdm\\Tests\\": "tests/"}}, "extra": {"symfony": {"allow-contrib": false, "require": "6.4.*"}}}