<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Lazy\LazyJsonArray;
use Vuryss\DoctrineLazyJsonOdm\Serializer\Serializer;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Address;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Product;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Profile;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapper;

describe('Performance Characteristics', function () {
    beforeEach(function () {
        $this->typeMapper = new TypeMapper([
            'user' => User::class,
            'product' => Product::class,
            'profile' => Profile::class,
            'address' => Address::class,
        ]);
        $this->serializer = new Serializer($this->typeMapper);
    });

    it('handles large arrays efficiently', function () {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        // Create large dataset
        $largeDataset = [];
        for ($i = 0; $i < 10000; ++$i) {
            $largeDataset[] = new User("User $i", "user$<EMAIL>");
        }

        $lazyArray = new LazyJsonArray($largeDataset);

        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        // Performance assertions
        $executionTime = $endTime - $startTime;
        $memoryUsed = $endMemory - $startMemory;

        expect($executionTime)->toBeLessThan(1.0); // Should complete in under 1 second
        expect($lazyArray->count())->toBe(10000);

        // Memory usage should be reasonable (less than 50MB for 10k objects)
        expect($memoryUsed)->toBeLessThan(50 * 1024 * 1024);
    });

    it('provides efficient random access to large arrays', function () {
        $largeDataset = [];
        for ($i = 0; $i < 1000; ++$i) {
            $largeDataset[] = new User("User $i", "user$<EMAIL>");
        }

        $lazyArray = new LazyJsonArray($largeDataset);

        $startTime = microtime(true);

        // Random access should be fast
        for ($i = 0; $i < 100; ++$i) {
            $randomIndex = rand(0, 999);
            $user = $lazyArray[$randomIndex];
            expect($user->getName())->toBe("User $randomIndex");
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        expect($executionTime)->toBeLessThan(0.1); // Should be very fast
    });

    it('serializes large datasets efficiently', function () {
        $dataset = [];
        for ($i = 0; $i < 1000; ++$i) {
            $dataset[] = new User("User $i", "user$<EMAIL>");
        }

        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        $json = $this->serializer->serialize($dataset);

        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        $executionTime = $endTime - $startTime;
        $memoryUsed = $endMemory - $startMemory;

        expect($executionTime)->toBeLessThan(2.0); // Should complete in under 2 seconds
        expect(strlen($json))->toBeGreaterThan(0);

        // Memory usage should be reasonable
        expect($memoryUsed)->toBeLessThan(100 * 1024 * 1024); // Less than 100MB
    });

    it('deserializes large datasets efficiently', function () {
        // Create test data
        $dataset = [];
        for ($i = 0; $i < 1000; ++$i) {
            $dataset[] = new User("User $i", "user$<EMAIL>");
        }

        $json = $this->serializer->serialize($dataset);

        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        $deserialized = $this->serializer->deserialize($json);

        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        $executionTime = $endTime - $startTime;
        $memoryUsed = $endMemory - $startMemory;

        expect($executionTime)->toBeLessThan(3.0); // Should complete in under 3 seconds
        expect($deserialized)->toBeArray();
        expect(count($deserialized))->toBe(1000);

        // Memory usage should be reasonable
        expect($memoryUsed)->toBeLessThan(150 * 1024 * 1024); // Less than 150MB
    });

    it('handles nested object serialization efficiently', function () {
        $users = [];
        for ($i = 0; $i < 100; ++$i) {
            $address = new Address("$i Main St", 'City', '12345', 'Country');
            $profile = new Profile("First$i", "Last$i", 25 + $i, "Bio $i", $address);
            $user = new User("User $i", "user$<EMAIL>");
            $user->setProfile($profile);
            $users[] = $user;
        }

        $startTime = microtime(true);

        $json = $this->serializer->serialize($users);
        $deserialized = $this->serializer->deserialize($json);

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        expect($executionTime)->toBeLessThan(2.0);
        expect(count($deserialized))->toBe(100);
        expect($deserialized[0]->getProfile())->toBeInstanceOf(Profile::class);
        expect($deserialized[0]->getProfile()->address)->toBeInstanceOf(Address::class);
    });

    it('demonstrates memory efficiency with type aliases', function () {
        $longTypeMapper = new TypeMapper([]);
        $shortTypeMapper = new TypeMapper([
            'u' => User::class,
            'p' => Profile::class,
            'a' => Address::class,
        ]);

        $longSerializer = new Serializer($longTypeMapper);
        $shortSerializer = new Serializer($shortTypeMapper);

        $users = [];
        for ($i = 0; $i < 100; ++$i) {
            $address = new Address("$i Main St", 'City', '12345', 'Country');
            $profile = new Profile("First$i", "Last$i", 25, 'Bio', $address);
            $user = new User("User $i", "user$<EMAIL>");
            $user->setProfile($profile);
            $users[] = $user;
        }

        $longJson = $longSerializer->serialize($users);
        $shortJson = $shortSerializer->serialize($users);

        // Short aliases should result in smaller JSON
        expect(strlen($shortJson))->toBeLessThan(strlen($longJson));

        // Verify both deserialize correctly
        $longDeserialized = $longSerializer->deserialize($longJson);
        $shortDeserialized = $shortSerializer->deserialize($shortJson);

        expect(count($longDeserialized))->toBe(count($shortDeserialized));
        expect($longDeserialized[0])->toBeInstanceOf(User::class);
        expect($shortDeserialized[0])->toBeInstanceOf(User::class);
    });

    it('maintains performance with repeated operations', function () {
        $users = [];
        for ($i = 0; $i < 100; ++$i) {
            $users[] = new User("User $i", "user$<EMAIL>");
        }

        $lazyArray = new LazyJsonArray($users);

        $startTime = microtime(true);

        // Perform multiple operations
        for ($i = 0; $i < 1000; ++$i) {
            $count = $lazyArray->count();
            $user = $lazyArray[$i % 100];
            $name = $user->getName();
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        expect($executionTime)->toBeLessThan(0.5); // Should be very fast
    });

    it('handles concurrent access patterns efficiently', function () {
        $users = [];
        for ($i = 0; $i < 1000; ++$i) {
            $users[] = new User("User $i", "user$<EMAIL>");
        }

        $lazyArray = new LazyJsonArray($users);

        $startTime = microtime(true);

        // Simulate concurrent access patterns
        $results = [];
        for ($i = 0; $i < 100; ++$i) {
            // Access different parts of the array
            $results[] = $lazyArray[$i * 10]->getName();
            $results[] = $lazyArray[($i * 10) + 5]->getEmail();
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        expect($executionTime)->toBeLessThan(0.2);
        expect(count($results))->toBe(200);
    });

    it('demonstrates lazy loading memory benefits', function () {
        // Create a large object that would consume significant memory
        $largeDataset = [];
        for ($i = 0; $i < 1000; ++$i) {
            $address = new Address(
                str_repeat("Street $i ", 10), // Large street name
                str_repeat("City $i ", 5),    // Large city name
                '12345',
                'Country'
            );
            $profile = new Profile(
                "First$i",
                "Last$i",
                25,
                str_repeat("Bio content $i ", 20), // Large bio
                $address,
                array_fill(0, 10, "social_link_$i") // Large social links array
            );
            $user = new User("User $i", "user$<EMAIL>");
            $user->setProfile($profile);
            $largeDataset[] = $user;
        }

        $startMemory = memory_get_usage();

        $lazyArray = new LazyJsonArray($largeDataset);

        // Access only a few items
        $user1 = $lazyArray[0];
        $user2 = $lazyArray[500];
        $user3 = $lazyArray[999];

        $endMemory = memory_get_usage();
        $memoryUsed = $endMemory - $startMemory;

        // Should not consume excessive memory for unused items
        expect($memoryUsed)->toBeLessThan(200 * 1024 * 1024); // Less than 200MB

        expect($user1)->toBeInstanceOf(User::class);
        expect($user2)->toBeInstanceOf(User::class);
        expect($user3)->toBeInstanceOf(User::class);
    });

    it('benchmarks array operations performance', function () {
        $users = [];
        for ($i = 0; $i < 1000; ++$i) {
            $users[] = new User("User $i", "user$<EMAIL>");
        }

        $lazyArray = new LazyJsonArray($users);

        // Benchmark different operations
        $operations = [
            'count' => fn () => $lazyArray->count(),
            'access_first' => fn () => $lazyArray[0],
            'access_middle' => fn () => $lazyArray[500],
            'access_last' => fn () => $lazyArray[999],
            'isset_check' => fn () => isset($lazyArray[500]),
        ];

        foreach ($operations as $name => $operation) {
            $startTime = microtime(true);

            for ($i = 0; $i < 1000; ++$i) {
                $operation();
            }

            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;

            // All operations should be fast
            expect($executionTime)->toBeLessThan(0.1, "Operation '$name' took too long: {$executionTime}s");
        }
    });
});
