<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Lazy\LazyJsonArray;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Product;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;

describe('LazyJsonArray', function () {
    beforeEach(function () {
        $this->user1 = new User('<PERSON>', '<EMAIL>');
        $this->user2 = new User('<PERSON>', '<EMAIL>');
        $this->product1 = new Product('Laptop', '999.99');
        $this->product2 = new Product('Mouse', '29.99');
    });

    it('can be instantiated with empty array', function () {
        $lazyArray = new LazyJsonArray([]);

        expect($lazyArray)->toBeInstanceOf(LazyJsonArray::class);
        expect($lazyArray->count())->toBe(0);
    });

    it('can be instantiated with items', function () {
        $items = [$this->user1, $this->user2];
        $lazyArray = new LazyJsonArray($items);

        expect($lazyArray)->toBeInstanceOf(LazyJsonArray::class);
        expect($lazyArray->count())->toBe(2);
    });

    it('implements ArrayAccess correctly', function () {
        $items = [$this->user1, $this->user2];
        $lazyArray = new LazyJsonArray($items);

        expect(isset($lazyArray[0]))->toBeTrue();
        expect(isset($lazyArray[1]))->toBeTrue();
        expect(isset($lazyArray[2]))->toBeFalse();

        expect($lazyArray[0])->toBe($this->user1);
        expect($lazyArray[1])->toBe($this->user2);
    });

    it('throws exception when trying to set value', function () {
        $lazyArray = new LazyJsonArray([$this->user1]);

        expect(fn () => $lazyArray[0] = $this->user2)
            ->toThrow(RuntimeException::class, 'The array is read-only. Cannot set value. Please replace the array instead.');
    });

    it('throws exception when trying to unset value', function () {
        $lazyArray = new LazyJsonArray([$this->user1]);

        expect(function () use ($lazyArray) { unset($lazyArray[0]); })
            ->toThrow(RuntimeException::class, 'The array is read-only. Cannot unset value. Please replace the array instead.');
    });

    it('implements Countable correctly', function () {
        $emptyArray = new LazyJsonArray([]);
        $singleArray = new LazyJsonArray([$this->user1]);
        $multiArray = new LazyJsonArray([$this->user1, $this->user2]);

        expect($emptyArray->count())->toBe(0);
        expect($singleArray->count())->toBe(1);
        expect($multiArray->count())->toBe(2);
    });

    it('implements IteratorAggregate correctly', function () {
        $items = [$this->user1, $this->user2];
        $lazyArray = new LazyJsonArray($items);

        $iteratedItems = [];
        foreach ($lazyArray as $key => $item) {
            $iteratedItems[$key] = $item;
        }

        expect($iteratedItems)->toBe($items);
    });

    it('can append items', function () {
        $lazyArray = new LazyJsonArray([$this->user1]);
        $newArray = $lazyArray->append($this->user2);

        expect($lazyArray->count())->toBe(1); // Original unchanged
        expect($newArray->count())->toBe(2);
        expect($newArray[0])->toBe($this->user1);
        expect($newArray[1])->toBe($this->user2);
    });

    it('can sort items', function () {
        $items = [$this->user2, $this->user1]; // Jane, John
        $lazyArray = new LazyJsonArray($items);

        $sortedArray = $lazyArray->sort(fn ($a, $b) => strcmp($a->getName(), $b->getName()));

        expect($lazyArray->count())->toBe(2); // Original unchanged
        expect($sortedArray->count())->toBe(2);
        expect($sortedArray[0]->getName())->toBe('Jane Smith');
        expect($sortedArray[1]->getName())->toBe('John Doe');
    });

    it('returns items array', function () {
        $items = [$this->user1, $this->user2];
        $lazyArray = new LazyJsonArray($items);

        expect($lazyArray->getItems())->toBe($items);
    });

    it('handles mixed object types', function () {
        $items = [$this->user1, $this->product1];
        $lazyArray = new LazyJsonArray($items);

        expect($lazyArray->count())->toBe(2);
        expect($lazyArray[0])->toBe($this->user1);
        expect($lazyArray[1])->toBe($this->product1);
    });

    it('can chain operations', function () {
        $lazyArray = new LazyJsonArray([$this->user1]);

        $result = $lazyArray
            ->append($this->user2)
            ->append($this->product1)
            ->sort(fn ($a, $b) => strcmp($a::class, $b::class));

        expect($result->count())->toBe(3);
        expect($result[0])->toBeInstanceOf(Product::class);
        expect($result[1])->toBeInstanceOf(User::class);
        expect($result[2])->toBeInstanceOf(User::class);
    });

    it('preserves object references', function () {
        $lazyArray = new LazyJsonArray([$this->user1]);

        expect($lazyArray[0])->toBe($this->user1);
        expect($lazyArray[0] === $this->user1)->toBeTrue();
    });

    it('handles large arrays efficiently', function () {
        $items = [];
        for ($i = 0; $i < 1000; ++$i) {
            $items[] = new User("User $i", "user$<EMAIL>");
        }

        $lazyArray = new LazyJsonArray($items);

        expect($lazyArray->count())->toBe(1000);
        expect($lazyArray[0]->getName())->toBe('User 0');
        expect($lazyArray[999]->getName())->toBe('User 999');
    });
});
