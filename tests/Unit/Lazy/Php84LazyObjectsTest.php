<?php

declare(strict_types=1);

use Doctrine\DBAL\Platforms\SqlitePlatform;
use Vuryss\DoctrineLazyJsonOdm\Lazy\LazyJsonArray;
use Vuryss\DoctrineLazyJsonOdm\Serializer\Serializer;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Address;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Profile;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;
use Vuryss\DoctrineLazyJsonOdm\Type\LazyJsonDocumentType;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapper;

describe('PHP 8.4 Lazy Objects', function () {
    beforeEach(function () {
        $this->typeMapper = new TypeMapper([
            'user' => User::class,
            'profile' => Profile::class,
            'address' => Address::class,
        ]);
        $this->serializer = new Serializer($this->typeMapper);
        $this->doctrineType = new LazyJsonDocumentType();
        $this->doctrineType->setSerializer($this->serializer);
        $this->doctrineType->setTypeMapper($this->typeMapper);
        $this->platform = new SqlitePlatform();
    });

    it('creates lazy ghost objects for single objects', function () {
        $address = new Address('123 Main St', 'New York', '10001', 'USA');
        $profile = new Profile('John', 'Doe', 30, 'Developer', $address);

        $json = $this->serializer->serialize($profile);

        // Convert to PHP value using Doctrine type (simulates lazy loading)
        $lazyProfile = $this->doctrineType->convertToPHPValue($json, $this->platform);

        // Should be a lazy proxy
        expect($lazyProfile)->toBeInstanceOf(Profile::class);

        // Accessing properties should work transparently
        expect($lazyProfile->firstName)->toBe('John');
        expect($lazyProfile->lastName)->toBe('Doe');
        expect($lazyProfile->age)->toBe(30);
        expect($lazyProfile->address)->toBeInstanceOf(Address::class);
    });

    it('creates lazy ghost objects for arrays', function () {
        $users = [
            new User('John Doe', '<EMAIL>'),
            new User('Jane Smith', '<EMAIL>'),
        ];

        $json = $this->serializer->serialize($users);

        // Convert to PHP value using Doctrine type
        $lazyArray = $this->doctrineType->convertToPHPValue($json, $this->platform);

        expect($lazyArray)->toBeInstanceOf(LazyJsonArray::class);
        expect($lazyArray->count())->toBe(2);
        expect($lazyArray[0])->toBeInstanceOf(User::class);
        expect($lazyArray[1])->toBeInstanceOf(User::class);
    });

    it('handles lazy initialization correctly', function () {
        $profile = new Profile('John', 'Doe', 30, 'Software Developer');
        $json = $this->serializer->serialize($profile);

        $lazyProfile = $this->doctrineType->convertToPHPValue($json, $this->platform);

        // Object should be lazy until accessed
        expect($lazyProfile)->toBeInstanceOf(Profile::class);

        // First access should trigger initialization
        $firstName = $lazyProfile->firstName;
        expect($firstName)->toBe('John');

        // Subsequent accesses should work normally
        expect($lazyProfile->lastName)->toBe('Doe');
        expect($lazyProfile->age)->toBe(30);
    });

    it('preserves object methods in lazy objects', function () {
        $address = new Address('123 Main St', 'New York', '10001', 'USA', 'NY');
        $profile = new Profile('John', 'Doe', 30, 'Developer', $address);

        $json = $this->serializer->serialize($profile);
        $lazyProfile = $this->doctrineType->convertToPHPValue($json, $this->platform);

        // Methods should work on lazy objects
        expect($lazyProfile->getFullName())->toBe('John Doe');
        expect($lazyProfile->address->getFormattedAddress())->toContain('123 Main St');
    });

    it('handles nested lazy objects correctly', function () {
        $address = new Address('123 Main St', 'New York', '10001', 'USA');
        $profile = new Profile('John', 'Doe', 30, 'Developer', $address);

        $json = $this->serializer->serialize($profile);
        $lazyProfile = $this->doctrineType->convertToPHPValue($json, $this->platform);

        // Nested objects should also be properly deserialized
        expect($lazyProfile->address)->toBeInstanceOf(Address::class);
        expect($lazyProfile->address->street)->toBe('123 Main St');
        expect($lazyProfile->address->city)->toBe('New York');
    });

    it('handles lazy arrays with mixed object types', function () {
        $objects = [
            new User('John Doe', '<EMAIL>'),
            new Profile('Jane', 'Smith', 25),
        ];

        $json = $this->serializer->serialize($objects);
        $lazyArray = $this->doctrineType->convertToPHPValue($json, $this->platform);

        expect($lazyArray)->toBeInstanceOf(LazyJsonArray::class);
        expect($lazyArray[0])->toBeInstanceOf(User::class);
        expect($lazyArray[1])->toBeInstanceOf(Profile::class);
    });

    it('maintains performance with lazy loading', function () {
        // Create a large object that would be expensive to deserialize
        $largeProfile = new Profile(
            'John',
            'Doe',
            30,
            str_repeat('Large bio content ', 1000), // Large bio
            new Address(str_repeat('Long street name ', 100), 'City', '12345', 'Country')
        );

        $json = $this->serializer->serialize($largeProfile);

        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        // Creating lazy object should be fast
        $lazyProfile = $this->doctrineType->convertToPHPValue($json, $this->platform);

        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        $creationTime = $endTime - $startTime;
        $memoryUsed = $endMemory - $startMemory;

        // Lazy object creation should be very fast
        expect($creationTime)->toBeLessThan(0.1);

        // Should not consume much memory until accessed
        expect($memoryUsed)->toBeLessThan(1024 * 1024); // Less than 1MB

        expect($lazyProfile)->toBeInstanceOf(Profile::class);
    });

    it('handles serialization of lazy objects back to database', function () {
        $profile = new Profile('John', 'Doe', 30);
        $json = $this->serializer->serialize($profile);

        // Create lazy object
        $lazyProfile = $this->doctrineType->convertToPHPValue($json, $this->platform);

        // Convert back to database value
        $databaseValue = $this->doctrineType->convertToDatabaseValue($lazyProfile, $this->platform);

        expect($databaseValue)->toBeString();

        // Should be able to deserialize again
        $deserializedProfile = $this->serializer->deserialize($databaseValue);
        expect($deserializedProfile)->toBeInstanceOf(Profile::class);
        expect($deserializedProfile->firstName)->toBe('John');
    });

    it('handles null values correctly', function () {
        $nullValue = $this->doctrineType->convertToPHPValue(null, $this->platform);
        expect($nullValue)->toBeNull();

        $emptyString = $this->doctrineType->convertToPHPValue('', $this->platform);
        expect($emptyString)->toBeNull();
    });

    it('handles arrays correctly with lazy ghost initialization', function () {
        $users = [];
        for ($i = 0; $i < 100; ++$i) {
            $users[] = new User("User $i", "user$<EMAIL>");
        }

        $json = $this->serializer->serialize($users);

        $startTime = microtime(true);

        $lazyArray = $this->doctrineType->convertToPHPValue($json, $this->platform);

        $endTime = microtime(true);
        $creationTime = $endTime - $startTime;

        // Creating lazy array should be fast even for large datasets
        expect($creationTime)->toBeLessThan(0.5);
        expect($lazyArray)->toBeInstanceOf(LazyJsonArray::class);
        expect($lazyArray->count())->toBe(100);
    });

    it('preserves type information in lazy objects', function () {
        $profile = new Profile('John', 'Doe', 30);
        $json = $this->serializer->serialize($profile);

        // JSON should contain type information
        $data = json_decode($json, true);
        expect($data)->toHaveKey('#type');
        expect($data['#type'])->toBe('profile');

        $lazyProfile = $this->doctrineType->convertToPHPValue($json, $this->platform);
        expect($lazyProfile)->toBeInstanceOf(Profile::class);
    });

    it('handles complex object graphs with lazy loading', function () {
        $address = new Address('123 Main St', 'New York', '10001', 'USA');
        $profile = new Profile('John', 'Doe', 30, 'Developer', $address);
        $user = new User('John Doe', '<EMAIL>');
        $user->setProfile($profile);

        $json = $this->serializer->serialize($user);
        $lazyUser = $this->doctrineType->convertToPHPValue($json, $this->platform);

        expect($lazyUser)->toBeInstanceOf(User::class);
        expect($lazyUser->getName())->toBe('John Doe');
        expect($lazyUser->getProfile())->toBeInstanceOf(Profile::class);
        expect($lazyUser->getProfile()->address)->toBeInstanceOf(Address::class);
        expect($lazyUser->getProfile()->address->street)->toBe('123 Main St');
    });

    it('demonstrates memory efficiency of lazy loading', function () {
        // Create objects with large data
        $users = [];
        for ($i = 0; $i < 10; ++$i) {
            $address = new Address(
                str_repeat("Street $i ", 50),
                str_repeat("City $i ", 20),
                '12345',
                'Country'
            );
            $profile = new Profile(
                "First$i",
                "Last$i",
                25,
                str_repeat("Bio $i ", 100),
                $address
            );
            $user = new User("User $i", "user$<EMAIL>");
            $user->setProfile($profile);
            $users[] = $user;
        }

        $json = $this->serializer->serialize($users);

        $startMemory = memory_get_usage();

        // Create lazy array
        $lazyArray = $this->doctrineType->convertToPHPValue($json, $this->platform);

        // Access only one item
        $firstUser = $lazyArray[0];
        $firstName = $firstUser->getName();

        $endMemory = memory_get_usage();
        $memoryUsed = $endMemory - $startMemory;

        // Should not load all objects into memory
        expect($memoryUsed)->toBeLessThan(10 * 1024 * 1024); // Less than 10MB
        expect($firstName)->toBe('User 0');
    });
});
