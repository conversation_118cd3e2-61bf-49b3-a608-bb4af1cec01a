<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Lazy\LazyJsonArray;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Product;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Profile;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;

describe('Lazy Loading Behavior', function () {
    beforeEach(function () {
        $this->users = [
            new User('<PERSON>', '<EMAIL>'),
            new User('<PERSON>', '<EMAIL>'),
            new User('<PERSON>', '<EMAIL>'),
        ];

        $this->products = [
            new Product('Laptop', '999.99'),
            new Product('Mouse', '29.99'),
            new Product('Keyboard', '79.99'),
        ];
    });

    it('creates lazy arrays without immediate initialization', function () {
        $lazyArray = new LazyJsonArray($this->users);

        // Array should be created but items should be accessible
        expect($lazyArray)->toBeInstanceOf(LazyJsonArray::class);
        expect($lazyArray->count())->toBe(3);
    });

    it('provides lazy access to array elements', function () {
        $lazyArray = new LazyJsonArray($this->users);

        // Accessing elements should work transparently
        expect($lazyArray[0])->toBe($this->users[0]);
        expect($lazyArray[1])->toBe($this->users[1]);
        expect($lazyArray[2])->toBe($this->users[2]);
    });

    it('supports lazy iteration over elements', function () {
        $lazyArray = new LazyJsonArray($this->users);

        $iteratedUsers = [];
        foreach ($lazyArray as $index => $user) {
            $iteratedUsers[$index] = $user;
        }

        expect($iteratedUsers)->toBe($this->users);
    });

    it('maintains lazy behavior with large datasets', function () {
        // Create a large dataset
        $largeDataset = [];
        for ($i = 0; $i < 1000; ++$i) {
            $largeDataset[] = new User("User $i", "user$<EMAIL>");
        }

        $lazyArray = new LazyJsonArray($largeDataset);

        // Should handle large datasets efficiently
        expect($lazyArray->count())->toBe(1000);
        expect($lazyArray[0]->getName())->toBe('User 0');
        expect($lazyArray[999]->getName())->toBe('User 999');
    });

    it('preserves object identity in lazy arrays', function () {
        $lazyArray = new LazyJsonArray($this->users);

        // Objects should maintain their identity
        expect($lazyArray[0] === $this->users[0])->toBeTrue();
        expect($lazyArray[1] === $this->users[1])->toBeTrue();
    });

    it('handles mixed object types in lazy arrays', function () {
        $mixedObjects = [
            $this->users[0],
            $this->products[0],
            $this->users[1],
            $this->products[1],
        ];

        $lazyArray = new LazyJsonArray($mixedObjects);

        expect($lazyArray[0])->toBeInstanceOf(User::class);
        expect($lazyArray[1])->toBeInstanceOf(Product::class);
        expect($lazyArray[2])->toBeInstanceOf(User::class);
        expect($lazyArray[3])->toBeInstanceOf(Product::class);
    });

    it('supports lazy operations without materializing entire array', function () {
        $lazyArray = new LazyJsonArray($this->users);

        // Operations should work without loading all items
        expect(isset($lazyArray[0]))->toBeTrue();
        expect(isset($lazyArray[10]))->toBeFalse();

        // Count should be available without iteration
        expect($lazyArray->count())->toBe(3);
    });

    it('handles empty lazy arrays correctly', function () {
        $emptyArray = new LazyJsonArray([]);

        expect($emptyArray->count())->toBe(0);
        expect(isset($emptyArray[0]))->toBeFalse();

        $items = [];
        foreach ($emptyArray as $item) {
            $items[] = $item;
        }
        expect($items)->toBe([]);
    });

    it('maintains immutability in lazy arrays', function () {
        $lazyArray = new LazyJsonArray($this->users);

        // Array should be read-only
        expect(fn () => $lazyArray[0] = new User('New User', '<EMAIL>'))
            ->toThrow(RuntimeException::class);

        expect(function () use ($lazyArray) { unset($lazyArray[0]); })
            ->toThrow(RuntimeException::class);
    });

    it('supports functional operations on lazy arrays', function () {
        $lazyArray = new LazyJsonArray($this->users);

        // Append operation creates new array
        $newArray = $lazyArray->append(new User('New User', '<EMAIL>'));

        expect($lazyArray->count())->toBe(3); // Original unchanged
        expect($newArray->count())->toBe(4);
        expect($newArray[3]->getName())->toBe('New User');
    });

    it('supports sorting without modifying original array', function () {
        $lazyArray = new LazyJsonArray($this->users);

        $sortedArray = $lazyArray->sort(fn ($a, $b) => strcmp($a->getName(), $b->getName()));

        expect($lazyArray->count())->toBe(3); // Original unchanged
        expect($sortedArray->count())->toBe(3);

        // Check sorting order
        $names = [];
        foreach ($sortedArray as $user) {
            $names[] = $user->getName();
        }
        expect($names)->toBe(['Bob Johnson', 'Jane Smith', 'John Doe']);
    });

    it('handles nested lazy structures', function () {
        $profile = new Profile('John', 'Doe', 30);
        $user = new User('John Doe', '<EMAIL>');
        $user->setProfile($profile);

        $lazyArray = new LazyJsonArray([$user]);

        // Nested objects should be accessible
        expect($lazyArray[0]->getProfile())->toBe($profile);
        expect($lazyArray[0]->getProfile()->firstName)->toBe('John');
    });

    it('provides efficient random access', function () {
        $lazyArray = new LazyJsonArray($this->users);

        // Random access should be O(1)
        expect($lazyArray[2]->getName())->toBe('Bob Johnson');
        expect($lazyArray[0]->getName())->toBe('John Doe');
        expect($lazyArray[1]->getName())->toBe('Jane Smith');
    });

    it('handles boundary conditions correctly', function () {
        $lazyArray = new LazyJsonArray($this->users);

        // Test boundary access
        expect(isset($lazyArray[-1]))->toBeFalse();
        expect(isset($lazyArray[3]))->toBeFalse();
        expect(isset($lazyArray[2]))->toBeTrue();
    });

    it('supports chaining of lazy operations', function () {
        $lazyArray = new LazyJsonArray([$this->users[1], $this->users[0]]); // Jane, John

        $result = $lazyArray
            ->append($this->users[2]) // Add Bob
            ->sort(fn ($a, $b) => strcmp($a->getName(), $b->getName())); // Sort alphabetically

        expect($result->count())->toBe(3);
        expect($result[0]->getName())->toBe('Bob Johnson');
        expect($result[1]->getName())->toBe('Jane Smith');
        expect($result[2]->getName())->toBe('John Doe');
    });

    it('maintains type safety in lazy arrays', function () {
        $lazyArray = new LazyJsonArray($this->users);

        foreach ($lazyArray as $item) {
            expect($item)->toBeInstanceOf(User::class);
        }

        // Type should be preserved through operations
        $newArray = $lazyArray->append(new User('New User', '<EMAIL>'));
        expect($newArray[3])->toBeInstanceOf(User::class);
    });
});
