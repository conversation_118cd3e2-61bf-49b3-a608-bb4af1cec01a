<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Serializer\Serializer;
use Vuryss\DoctrineLazyJsonOdm\Serializer\SerializerInterface;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Product;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Profile;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapper;

describe('Type Alias Mapping', function () {
    it('uses short aliases instead of full class names', function () {
        $typeMapper = new TypeMapper([
            'u' => User::class,
            'p' => Profile::class,
            'prod' => Product::class,
        ]);
        $serializer = new Serializer($typeMapper);

        $user = new User('John Doe', '<EMAIL>');

        $json = $serializer->serialize($user);
        $data = json_decode($json, true);

        expect($data[SerializerInterface::TYPE_KEY])->toBe('u');
        expect($data[SerializerInterface::TYPE_KEY])->not->toBe(User::class);
    });

    it('deserializes using short aliases', function () {
        $typeMapper = new TypeMapper([
            'u' => User::class,
            'p' => Profile::class,
        ]);
        $serializer = new Serializer($typeMapper);

        $json = json_encode([
            SerializerInterface::TYPE_KEY => 'u',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'createdAt' => '2024-01-01T00:00:00+00:00',
        ]);

        $user = $serializer->deserialize($json);

        expect($user)->toBeInstanceOf(User::class);
        expect($user->getName())->toBe('John Doe');
    });

    it('falls back to full class names when no alias is configured', function () {
        $typeMapper = new TypeMapper([]); // No mappings
        $serializer = new Serializer($typeMapper);

        $user = new User('John Doe', '<EMAIL>');

        $json = $serializer->serialize($user);
        $data = json_decode($json, true);

        expect($data[SerializerInterface::TYPE_KEY])->toBe(User::class);
    });

    it('deserializes using full class names when no alias is configured', function () {
        $typeMapper = new TypeMapper([]);
        $serializer = new Serializer($typeMapper);

        $json = json_encode([
            SerializerInterface::TYPE_KEY => User::class,
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'createdAt' => '2024-01-01T00:00:00+00:00',
        ]);

        $user = $serializer->deserialize($json);

        expect($user)->toBeInstanceOf(User::class);
    });

    it('handles mixed alias and full class name scenarios', function () {
        $typeMapper = new TypeMapper([
            'user' => User::class,
            // Profile not mapped, will use full class name
        ]);
        $serializer = new Serializer($typeMapper);

        $profile = new Profile('John', 'Doe', 30);
        $user = new User('John Doe', '<EMAIL>');
        $user->setProfile($profile);

        $json = $serializer->serialize($user);
        $data = json_decode($json, true);

        expect($data[SerializerInterface::TYPE_KEY])->toBe('user');
        expect($data['profile'][SerializerInterface::TYPE_KEY])->toBe(Profile::class);
    });

    it('optimizes storage with short aliases', function () {
        $longTypeMapper = new TypeMapper([]);
        $shortTypeMapper = new TypeMapper([
            'u' => User::class,
            'p' => Profile::class,
        ]);

        $longSerializer = new Serializer($longTypeMapper);
        $shortSerializer = new Serializer($shortTypeMapper);

        $profile = new Profile('John', 'Doe', 30);
        $user = new User('John Doe', '<EMAIL>');
        $user->setProfile($profile);

        $longJson = $longSerializer->serialize($user);
        $shortJson = $shortSerializer->serialize($user);

        // Short aliases should result in smaller JSON
        expect(strlen($shortJson))->toBeLessThan(strlen($longJson));

        // But both should deserialize to the same object structure
        $longDeserialized = $longSerializer->deserialize($longJson);
        $shortDeserialized = $shortSerializer->deserialize($shortJson);

        expect($longDeserialized)->toBeInstanceOf(User::class);
        expect($shortDeserialized)->toBeInstanceOf(User::class);
        expect($longDeserialized->getName())->toBe($shortDeserialized->getName());
    });

    it('handles namespace-based aliases', function () {
        $typeMapper = new TypeMapper([
            'entity.user' => User::class,
            'entity.profile' => Profile::class,
            'catalog.product' => Product::class,
        ]);
        $serializer = new Serializer($typeMapper);

        $user = new User('John Doe', '<EMAIL>');
        $product = new Product('Laptop', '999.99');

        $userJson = $serializer->serialize($user);
        $productJson = $serializer->serialize($product);

        $userData = json_decode($userJson, true);
        $productData = json_decode($productJson, true);

        expect($userData[SerializerInterface::TYPE_KEY])->toBe('entity.user');
        expect($productData[SerializerInterface::TYPE_KEY])->toBe('catalog.product');
    });

    it('handles special characters in aliases', function () {
        $typeMapper = new TypeMapper([
            'user-profile' => Profile::class,
            'user_entity' => User::class,
            'product@v1' => Product::class,
        ]);
        $serializer = new Serializer($typeMapper);

        $user = new User('John Doe', '<EMAIL>');
        $profile = new Profile('John', 'Doe', 30);
        $product = new Product('Laptop', '999.99');

        $userJson = $serializer->serialize($user);
        $profileJson = $serializer->serialize($profile);
        $productJson = $serializer->serialize($product);

        $userData = json_decode($userJson, true);
        $profileData = json_decode($profileJson, true);
        $productData = json_decode($productJson, true);

        expect($userData[SerializerInterface::TYPE_KEY])->toBe('user_entity');
        expect($profileData[SerializerInterface::TYPE_KEY])->toBe('user-profile');
        expect($productData[SerializerInterface::TYPE_KEY])->toBe('product@v1');

        // Verify deserialization works
        $deserializedUser = $serializer->deserialize($userJson);
        $deserializedProfile = $serializer->deserialize($profileJson);
        $deserializedProduct = $serializer->deserialize($productJson);

        expect($deserializedUser)->toBeInstanceOf(User::class);
        expect($deserializedProfile)->toBeInstanceOf(Profile::class);
        expect($deserializedProduct)->toBeInstanceOf(Product::class);
    });

    it('maintains consistency across serialization cycles', function () {
        $typeMapper = new TypeMapper([
            'user' => User::class,
            'profile' => Profile::class,
        ]);
        $serializer = new Serializer($typeMapper);

        $profile = new Profile('John', 'Doe', 30);
        $user = new User('John Doe', '<EMAIL>');
        $user->setProfile($profile);

        // Multiple serialization/deserialization cycles
        for ($i = 0; $i < 3; ++$i) {
            $json = $serializer->serialize($user);
            $user = $serializer->deserialize($json);

            expect($user)->toBeInstanceOf(User::class);
            expect($user->getProfile())->toBeInstanceOf(Profile::class);
            expect($user->getName())->toBe('John Doe');
            expect($user->getProfile()->firstName)->toBe('John');
        }
    });

    it('handles case-sensitive aliases correctly', function () {
        $typeMapper = new TypeMapper([
            'User' => User::class,
            'user' => Profile::class, // Different case, different class
        ]);
        $serializer = new Serializer($typeMapper);

        $user = new User('John Doe', '<EMAIL>');
        $profile = new Profile('John', 'Doe', 30);

        $userJson = $serializer->serialize($user);
        $profileJson = $serializer->serialize($profile);

        $userData = json_decode($userJson, true);
        $profileData = json_decode($profileJson, true);

        expect($userData[SerializerInterface::TYPE_KEY])->toBe('User');
        expect($profileData[SerializerInterface::TYPE_KEY])->toBe('user');

        $deserializedUser = $serializer->deserialize($userJson);
        $deserializedProfile = $serializer->deserialize($profileJson);

        expect($deserializedUser)->toBeInstanceOf(User::class);
        expect($deserializedProfile)->toBeInstanceOf(Profile::class);
    });

    it('handles arrays with mixed alias types', function () {
        $typeMapper = new TypeMapper([
            'u' => User::class,
            'p' => Product::class,
        ]);
        $serializer = new Serializer($typeMapper);

        $objects = [
            new User('John Doe', '<EMAIL>'),
            new Product('Laptop', '999.99'),
            new Profile('Jane', 'Smith', 25), // No alias, will use full class name
        ];

        $json = $serializer->serialize($objects);
        $data = json_decode($json, true);

        expect($data[0][SerializerInterface::TYPE_KEY])->toBe('u');
        expect($data[1][SerializerInterface::TYPE_KEY])->toBe('p');
        expect($data[2][SerializerInterface::TYPE_KEY])->toBe(Profile::class);

        $deserialized = $serializer->deserialize($json);

        expect($deserialized[0])->toBeInstanceOf(User::class);
        expect($deserialized[1])->toBeInstanceOf(Product::class);
        expect($deserialized[2])->toBeInstanceOf(Profile::class);
    });
});
