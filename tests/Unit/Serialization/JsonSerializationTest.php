<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Serializer\Serializer;
use Vuryss\DoctrineLazyJsonOdm\Serializer\SerializerInterface;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Address;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Product;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\ProductSpecifications;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Profile;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapper;

describe('JSON Serialization', function () {
    beforeEach(function () {
        $this->typeMapper = new TypeMapper([
            'user' => User::class,
            'profile' => Profile::class,
            'address' => Address::class,
            'product' => Product::class,
            'product_specs' => ProductSpecifications::class,
        ]);
        $this->serializer = new Serializer($this->typeMapper);
    });

    it('serializes objects with type metadata', function () {
        $user = new User('John Doe', '<EMAIL>');

        $json = $this->serializer->serialize($user);
        $data = json_decode($json, true);

        expect($data)->toHaveKey(SerializerInterface::TYPE_KEY);
        expect($data[SerializerInterface::TYPE_KEY])->toBe('user');
        expect($data)->toHaveKey('name', 'John Doe');
        expect($data)->toHaveKey('email', '<EMAIL>');
    });

    it('deserializes objects using type metadata', function () {
        $json = json_encode([
            SerializerInterface::TYPE_KEY => 'user',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'createdAt' => '2024-01-01T00:00:00+00:00',
        ]);

        $user = $this->serializer->deserialize($json);

        expect($user)->toBeInstanceOf(User::class);
        expect($user->getName())->toBe('John Doe');
        expect($user->getEmail())->toBe('<EMAIL>');
    });

    it('preserves type metadata in nested objects', function () {
        $address = new Address('123 Main St', 'New York', '10001', 'USA');
        $profile = new Profile('John', 'Doe', 30, 'Developer', $address);

        $json = $this->serializer->serialize($profile);
        $data = json_decode($json, true);

        expect($data[SerializerInterface::TYPE_KEY])->toBe('profile');
        expect($data['address'])->toHaveKey(SerializerInterface::TYPE_KEY);
        expect($data['address'][SerializerInterface::TYPE_KEY])->toBe('address');
    });

    it('handles flat JSON structure correctly', function () {
        $specs = new ProductSpecifications(
            dimensions: ['width' => 30, 'height' => 20, 'depth' => 5],
            weight: '2.5kg',
            material: 'Aluminum',
            color: 'Silver'
        );

        $json = $this->serializer->serialize($specs);
        $data = json_decode($json, true);

        // Should be flat structure with type metadata
        expect($data[SerializerInterface::TYPE_KEY])->toBe('product_specs');
        expect($data['dimensions'])->toBe(['width' => 30, 'height' => 20, 'depth' => 5]);
        expect($data['weight'])->toBe('2.5kg');
        expect($data['material'])->toBe('Aluminum');
        expect($data['color'])->toBe('Silver');
    });

    it('serializes arrays with type metadata for each item', function () {
        $users = [
            new User('John Doe', '<EMAIL>'),
            new User('Jane Smith', '<EMAIL>'),
        ];

        $json = $this->serializer->serialize($users);
        $data = json_decode($json, true);

        expect($data)->toBeArray();
        expect($data)->toHaveCount(2);

        foreach ($data as $item) {
            expect($item)->toHaveKey(SerializerInterface::TYPE_KEY);
            expect($item[SerializerInterface::TYPE_KEY])->toBe('user');
        }
    });

    it('deserializes arrays maintaining object types', function () {
        $json = json_encode([
            [
                SerializerInterface::TYPE_KEY => 'user',
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'createdAt' => '2024-01-01T00:00:00+00:00',
            ],
            [
                SerializerInterface::TYPE_KEY => 'user',
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'createdAt' => '2024-01-01T00:00:00+00:00',
            ],
        ]);

        $users = $this->serializer->deserialize($json);

        expect($users)->toBeArray();
        expect($users)->toHaveCount(2);
        expect($users[0])->toBeInstanceOf(User::class);
        expect($users[1])->toBeInstanceOf(User::class);
    });

    it('handles mixed object types in arrays', function () {
        $objects = [
            new User('John Doe', '<EMAIL>'),
            new Product('Laptop', '999.99'),
        ];

        $json = $this->serializer->serialize($objects);
        $data = json_decode($json, true);

        expect($data[0][SerializerInterface::TYPE_KEY])->toBe('user');
        expect($data[1][SerializerInterface::TYPE_KEY])->toBe('product');
    });

    it('preserves null values in serialization', function () {
        $user = new User('John Doe', '<EMAIL>');
        // Profile is null by default

        $json = $this->serializer->serialize($user);
        $data = json_decode($json, true);

        expect($data)->toHaveKey('profile');
        expect($data['profile'])->toBeNull();
    });

    it('handles complex nested structures', function () {
        $address = new Address('123 Main St', 'New York', '10001', 'USA', 'NY');
        $profile = new Profile(
            'John',
            'Doe',
            30,
            'Software Developer',
            $address,
            ['github' => 'johndoe', 'linkedin' => 'john-doe']
        );
        $user = new User('John Doe', '<EMAIL>');
        $user->setProfile($profile);

        $json = $this->serializer->serialize($user);
        $deserialized = $this->serializer->deserialize($json);

        expect($deserialized)->toBeInstanceOf(User::class);
        expect($deserialized->getProfile())->toBeInstanceOf(Profile::class);
        expect($deserialized->getProfile()->address)->toBeInstanceOf(Address::class);
        expect($deserialized->getProfile()->address->state)->toBe('NY');
        expect($deserialized->getProfile()->socialLinks)->toBe(['github' => 'johndoe', 'linkedin' => 'john-doe']);
    });

    it('maintains data integrity through serialization round trip', function () {
        $specs = new ProductSpecifications(
            dimensions: ['width' => 30.5, 'height' => 20.2, 'depth' => 5.1],
            weight: '2.5kg',
            material: 'Aluminum',
            color: 'Silver',
            features: ['waterproof', 'lightweight', 'durable'],
            technicalSpecs: [
                'cpu' => 'Intel i7',
                'ram' => '16GB',
                'storage' => '512GB SSD',
            ]
        );

        $json = $this->serializer->serialize($specs);
        $deserialized = $this->serializer->deserialize($json);

        expect($deserialized)->toBeInstanceOf(ProductSpecifications::class);
        expect($deserialized->dimensions)->toBe(['width' => 30.5, 'height' => 20.2, 'depth' => 5.1]);
        expect($deserialized->weight)->toBe('2.5kg');
        expect($deserialized->features)->toBe(['waterproof', 'lightweight', 'durable']);
        expect($deserialized->technicalSpecs['cpu'])->toBe('Intel i7');
    });

    it('handles DateTime objects correctly', function () {
        $user = new User('John Doe', '<EMAIL>');

        $json = $this->serializer->serialize($user);
        $deserialized = $this->serializer->deserialize($json);

        expect($deserialized->getCreatedAt())->toBeInstanceOf(DateTimeImmutable::class);
        expect($deserialized->getCreatedAt()->format('Y-m-d'))->toBe($user->getCreatedAt()->format('Y-m-d'));
    });

    it('handles empty arrays and objects', function () {
        $profile = new Profile('John', 'Doe', 30);

        $json = $this->serializer->serialize($profile);
        $data = json_decode($json, true);

        expect($data['socialLinks'])->toBe([]);
        expect($data['address'])->toBeNull();

        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized->socialLinks)->toBe([]);
        expect($deserialized->address)->toBeNull();
    });
});
