<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Serializer\Serializer;
use Vuryss\DoctrineLazyJsonOdm\Serializer\SerializerException;
use Vuryss\DoctrineLazyJsonOdm\Serializer\SerializerInterface;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapper;

describe('Serializer Interface', function () {
    beforeEach(function () {
        $this->typeMapper = new TypeMapper(['user' => User::class]);
        $this->serializer = new Serializer($this->typeMapper);
    });

    it('implements SerializerInterface', function () {
        expect($this->serializer)->toBeInstanceOf(SerializerInterface::class);
    });

    it('has correct TYPE_KEY constant', function () {
        expect(SerializerInterface::TYPE_KEY)->toBe('#type');
    });

    it('serialize method returns string', function () {
        $user = new User('<PERSON> Do<PERSON>', '<EMAIL>');

        $result = $this->serializer->serialize($user);

        expect($result)->toBeString();
    });

    it('serialize method accepts context parameter', function () {
        $user = new User('John Doe', '<EMAIL>');
        $context = ['groups' => ['public']];

        $result = $this->serializer->serialize($user, $context);

        expect($result)->toBeString();
    });

    it('serialize method throws SerializerException on error', function () {
        // Test with a resource that cannot be serialized
        $resource = fopen('php://memory', 'r');

        expect(fn () => $this->serializer->serialize($resource))
            ->toThrow(SerializerException::class);

        fclose($resource);
    });

    it('deserialize method returns mixed type', function () {
        $json = '{"#type":"user","name":"John Doe","email":"<EMAIL>","createdAt":"2024-01-01T00:00:00+00:00"}';

        $result = $this->serializer->deserialize($json);

        expect($result)->toBeInstanceOf(User::class);
    });

    it('deserialize method accepts context parameter', function () {
        $json = '{"#type":"user","name":"John Doe","email":"<EMAIL>","createdAt":"2024-01-01T00:00:00+00:00"}';
        $context = ['groups' => ['public']];

        $result = $this->serializer->deserialize($json, $context);

        expect($result)->toBeInstanceOf(User::class);
    });

    it('deserialize method throws SerializerException on error', function () {
        $invalidJson = '{"invalid": json}';

        expect(fn () => $this->serializer->deserialize($invalidJson))
            ->toThrow(SerializerException::class);
    });

    it('can be used polymorphically', function () {
        $serializers = [
            new Serializer($this->typeMapper),
            // Could add other implementations here
        ];

        foreach ($serializers as $serializer) {
            expect($serializer)->toBeInstanceOf(SerializerInterface::class);

            $user = new User('John Doe', '<EMAIL>');
            $json = $serializer->serialize($user);
            $deserialized = $serializer->deserialize($json);

            expect($deserialized)->toBeInstanceOf(User::class);
            expect($deserialized->getName())->toBe('John Doe');
        }
    });

    it('maintains consistent interface across different data types', function () {
        $testCases = [
            'null' => null,
            'string' => 'test string',
            'integer' => 42,
            'float' => 3.14,
            'boolean_true' => true,
            'boolean_false' => false,
            'array' => ['a', 'b', 'c'],
            'object' => new User('John', '<EMAIL>'),
        ];

        foreach ($testCases as $name => $value) {
            $json = $this->serializer->serialize($value);
            expect($json)->toBeString("Failed to serialize $name");

            $deserialized = $this->serializer->deserialize($json);

            if ($value instanceof User) {
                expect($deserialized)->toBeInstanceOf(User::class);
                expect($deserialized->getName())->toBe($value->getName());
            } else {
                expect($deserialized)->toBe($value, "Failed round-trip for $name");
            }
        }
    });

    it('handles context consistently across serialize and deserialize', function () {
        $user = new User('John Doe', '<EMAIL>');
        $context = ['groups' => ['public'], 'custom' => 'value'];

        // Both methods should accept context without throwing
        $json = $this->serializer->serialize($user, $context);
        $deserialized = $this->serializer->deserialize($json, $context);

        expect($deserialized)->toBeInstanceOf(User::class);
    });

    it('provides consistent error handling', function () {
        // Both methods should throw SerializerException for errors
        expect(fn () => $this->serializer->serialize(fopen('php://memory', 'r')))
            ->toThrow(SerializerException::class);

        expect(fn () => $this->serializer->deserialize('invalid json'))
            ->toThrow(SerializerException::class);
    });
});

describe('Mock Serializer Implementation', function () {
    it('can create alternative serializer implementations', function () {
        $mockSerializer = new class implements SerializerInterface {
            public function serialize(mixed $data, array $context = []): string
            {
                return json_encode([
                    self::TYPE_KEY => 'mock',
                    'data' => $data,
                    'context' => $context,
                ]);
            }

            public function deserialize(string $json, array $context = []): mixed
            {
                $data = json_decode($json, true);

                return $data['data'] ?? null;
            }
        };

        expect($mockSerializer)->toBeInstanceOf(SerializerInterface::class);

        $result = $mockSerializer->serialize('test', ['key' => 'value']);
        $data = json_decode($result, true);

        expect($data[SerializerInterface::TYPE_KEY])->toBe('mock');
        expect($data['data'])->toBe('test');
        expect($data['context'])->toBe(['key' => 'value']);

        $deserialized = $mockSerializer->deserialize($result);
        expect($deserialized)->toBe('test');
    });

    it('demonstrates interface flexibility for different serializer backends', function () {
        // This test shows how the interface allows for different implementations
        $serializers = [
            'vuryss' => new Serializer(new TypeMapper()),
            'mock' => new class implements SerializerInterface {
                public function serialize(mixed $data, array $context = []): string
                {
                    return 'mock:'.serialize($data);
                }

                public function deserialize(string $json, array $context = []): mixed
                {
                    return unserialize(substr($json, 5));
                }
            },
        ];

        foreach ($serializers as $name => $serializer) {
            expect($serializer)->toBeInstanceOf(SerializerInterface::class);

            $testData = ['test' => 'data'];
            $serialized = $serializer->serialize($testData);
            $deserialized = $serializer->deserialize($serialized);

            expect($deserialized)->toBe($testData, "Failed for $name serializer");
        }
    });
});
