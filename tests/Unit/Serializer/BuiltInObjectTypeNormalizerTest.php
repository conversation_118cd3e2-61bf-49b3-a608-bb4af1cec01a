<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Serializer\BuiltInObjectTypeNormalizer;
use Vuryss\DoctrineLazyJsonOdm\Serializer\SerializerInterface;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Profile;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapper;
use Vuryss\Serializer\Normalizer;

describe('BuiltInObjectTypeNormalizer', function () {
    beforeEach(function () {
        $this->typeMapper = new TypeMapper([
            'user' => User::class,
            'profile' => Profile::class,
        ]);
        $this->normalizer = new BuiltInObjectTypeNormalizer($this->typeMapper);
        $this->mockNormalizer = Mockery::mock(Normalizer::class);
    });

    afterEach(function () {
        Mockery::close();
    });

    it('can be instantiated', function () {
        expect($this->normalizer)->toBeInstanceOf(BuiltInObjectTypeNormalizer::class);
    });

    it('supports normalization of objects', function () {
        $user = new User('John Doe', '<EMAIL>');

        expect($this->normalizer->supportsNormalization($user))->toBeTrue();
    });

    it('does not support normalization of non-objects', function () {
        expect($this->normalizer->supportsNormalization('string'))->toBeFalse();
        expect($this->normalizer->supportsNormalization(123))->toBeFalse();
        expect($this->normalizer->supportsNormalization(null))->toBeFalse();
        expect($this->normalizer->supportsNormalization([]))->toBeFalse();
    });

    it('adds type key to normalized object', function () {
        $user = new User('John Doe', '<EMAIL>');

        // Mock the ObjectNormalizer behavior
        $this->mockNormalizer
            ->shouldReceive('normalize')
            ->once()
            ->andReturn([
                'name' => 'John Doe',
                'email' => '<EMAIL>',
            ]);

        $result = $this->normalizer->normalize($user, $this->mockNormalizer, []);

        expect($result)->toBeArray();
        expect($result)->toHaveKey(SerializerInterface::TYPE_KEY);
        expect($result[SerializerInterface::TYPE_KEY])->toBe('user');
        expect($result)->toHaveKey('name', 'John Doe');
        expect($result)->toHaveKey('email', '<EMAIL>');
    });

    it('uses type mapper to get type alias', function () {
        $profile = new Profile('John', 'Doe', 30);

        $this->mockNormalizer
            ->shouldReceive('normalize')
            ->once()
            ->andReturn([
                'firstName' => 'John',
                'lastName' => 'Doe',
                'age' => 30,
            ]);

        $result = $this->normalizer->normalize($profile, $this->mockNormalizer, []);

        expect($result[SerializerInterface::TYPE_KEY])->toBe('profile');
    });

    it('uses full class name when no alias is configured', function () {
        $typeMapper = new TypeMapper([]); // No mappings
        $normalizer = new BuiltInObjectTypeNormalizer($typeMapper);

        $user = new User('John Doe', '<EMAIL>');

        $this->mockNormalizer
            ->shouldReceive('normalize')
            ->once()
            ->andReturn([
                'name' => 'John Doe',
                'email' => '<EMAIL>',
            ]);

        $result = $normalizer->normalize($user, $this->mockNormalizer, []);

        expect($result[SerializerInterface::TYPE_KEY])->toBe(User::class);
    });

    it('preserves original normalized data', function () {
        $user = new User('John Doe', '<EMAIL>');

        $originalData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'createdAt' => '2024-01-01T00:00:00+00:00',
            'profile' => null,
        ];

        $this->mockNormalizer
            ->shouldReceive('normalize')
            ->once()
            ->andReturn($originalData);

        $result = $this->normalizer->normalize($user, $this->mockNormalizer, []);

        // Type key should be first
        expect($result[SerializerInterface::TYPE_KEY])->toBe('user');

        // All original data should be preserved
        foreach ($originalData as $key => $value) {
            expect($result)->toHaveKey($key, $value);
        }
    });

    it('passes context to underlying normalizer', function () {
        $user = new User('John Doe', '<EMAIL>');
        $context = ['groups' => ['public']];

        $this->mockNormalizer
            ->shouldReceive('normalize')
            ->once()
            ->with($user, Mockery::any(), $context)
            ->andReturn(['name' => 'John Doe']);

        $this->normalizer->normalize($user, $this->mockNormalizer, $context);
    });

    it('handles objects with existing type key', function () {
        $user = new User('John Doe', '<EMAIL>');

        // Simulate ObjectNormalizer returning data with existing #type key
        $this->mockNormalizer
            ->shouldReceive('normalize')
            ->once()
            ->andReturn([
                '#type' => 'existing',
                'name' => 'John Doe',
            ]);

        $result = $this->normalizer->normalize($user, $this->mockNormalizer, []);

        // Our type should override the existing one
        expect($result[SerializerInterface::TYPE_KEY])->toBe('user');
        expect($result)->toHaveKey('name', 'John Doe');
    });

    it('handles anonymous classes', function () {
        $anonymousObject = new class {
            public string $property = 'value';
        };

        $this->mockNormalizer
            ->shouldReceive('normalize')
            ->once()
            ->andReturn(['property' => 'value']);

        $result = $this->normalizer->normalize($anonymousObject, $this->mockNormalizer, []);

        expect($result)->toHaveKey(SerializerInterface::TYPE_KEY);
        expect($result[SerializerInterface::TYPE_KEY])->toContain('class@anonymous');
    });

    it('handles stdClass objects', function () {
        $stdObject = new stdClass();
        $stdObject->property = 'value';

        $this->mockNormalizer
            ->shouldReceive('normalize')
            ->once()
            ->andReturn(['property' => 'value']);

        $result = $this->normalizer->normalize($stdObject, $this->mockNormalizer, []);

        expect($result[SerializerInterface::TYPE_KEY])->toBe('stdClass');
    });
});
