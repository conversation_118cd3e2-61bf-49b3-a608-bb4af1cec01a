<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Serializer\Serializer;
use Vuryss\DoctrineLazyJsonOdm\Serializer\SerializerException;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Address;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Profile;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapper;

describe('Serializer', function () {
    beforeEach(function () {
        $this->typeMapper = new TypeMapper([
            'user' => User::class,
            'profile' => Profile::class,
            'address' => Address::class,
        ]);
        $this->serializer = new Serializer($this->typeMapper);
    });

    it('can be instantiated', function () {
        expect($this->serializer)->toBeInstanceOf(Serializer::class);
    });

    it('can serialize simple objects', function () {
        $user = new User('John Doe', '<EMAIL>');

        $json = $this->serializer->serialize($user);

        expect($json)->toBeString();
        $data = json_decode($json, true);
        expect($data)->toHaveKey('#type', 'user');
        expect($data)->toHaveKey('name', 'John Doe');
        expect($data)->toHaveKey('email', '<EMAIL>');
    });

    it('can deserialize simple objects', function () {
        $json = '{"#type":"user","name":"John Doe","email":"<EMAIL>","createdAt":"2024-01-01T00:00:00+00:00"}';

        $user = $this->serializer->deserialize($json);

        expect($user)->toBeInstanceOf(User::class);
        expect($user->getName())->toBe('John Doe');
        expect($user->getEmail())->toBe('<EMAIL>');
    });

    it('can serialize nested objects', function () {
        $address = new Address('123 Main St', 'New York', '10001', 'USA');
        $profile = new Profile('John', 'Doe', 30, 'Software Developer', $address);

        $json = $this->serializer->serialize($profile);

        expect($json)->toBeString();
        $data = json_decode($json, true);
        expect($data)->toHaveKey('#type', 'profile');
        expect($data)->toHaveKey('firstName', 'John');
        expect($data)->toHaveKey('address');
        expect($data['address'])->toHaveKey('#type', 'address');
        expect($data['address'])->toHaveKey('street', '123 Main St');
    });

    it('can deserialize nested objects', function () {
        $json = '{"#type":"profile","firstName":"John","lastName":"Doe","age":30,"bio":"Software Developer","address":{"#type":"address","street":"123 Main St","city":"New York","postalCode":"10001","country":"USA","state":null},"socialLinks":[]}';

        $profile = $this->serializer->deserialize($json);

        expect($profile)->toBeInstanceOf(Profile::class);
        expect($profile->firstName)->toBe('John');
        expect($profile->address)->toBeInstanceOf(Address::class);
        expect($profile->address->street)->toBe('123 Main St');
    });

    it('can serialize arrays', function () {
        $users = [
            new User('John Doe', '<EMAIL>'),
            new User('Jane Smith', '<EMAIL>'),
        ];

        $json = $this->serializer->serialize($users);

        expect($json)->toBeString();
        $data = json_decode($json, true);
        expect($data)->toBeArray();
        expect($data)->toHaveCount(2);
        expect($data[0])->toHaveKey('#type', 'user');
        expect($data[1])->toHaveKey('#type', 'user');
    });

    it('can deserialize arrays', function () {
        $json = '[{"#type":"user","name":"John Doe","email":"<EMAIL>","createdAt":"2024-01-01T00:00:00+00:00"},{"#type":"user","name":"Jane Smith","email":"<EMAIL>","createdAt":"2024-01-01T00:00:00+00:00"}]';

        $users = $this->serializer->deserialize($json);

        expect($users)->toBeArray();
        expect($users)->toHaveCount(2);
        expect($users[0])->toBeInstanceOf(User::class);
        expect($users[1])->toBeInstanceOf(User::class);
        expect($users[0]->getName())->toBe('John Doe');
        expect($users[1]->getName())->toBe('Jane Smith');
    });

    it('can serialize null values', function () {
        $json = $this->serializer->serialize(null);

        expect($json)->toBe('null');
    });

    it('can deserialize null values', function () {
        $result = $this->serializer->deserialize('null');

        expect($result)->toBeNull();
    });

    it('can serialize primitive types', function () {
        expect($this->serializer->serialize('string'))->toBe('"string"');
        expect($this->serializer->serialize(123))->toBe('123');
        expect($this->serializer->serialize(12.34))->toBe('12.34');
        expect($this->serializer->serialize(true))->toBe('true');
        expect($this->serializer->serialize(false))->toBe('false');
    });

    it('can deserialize primitive types', function () {
        expect($this->serializer->deserialize('"string"'))->toBe('string');
        expect($this->serializer->deserialize('123'))->toBe(123);
        expect($this->serializer->deserialize('12.34'))->toBe(12.34);
        expect($this->serializer->deserialize('true'))->toBe(true);
        expect($this->serializer->deserialize('false'))->toBe(false);
    });

    it('handles serialization context', function () {
        $user = new User('John Doe', '<EMAIL>');

        $json = $this->serializer->serialize($user, ['groups' => ['public']]);

        expect($json)->toBeString();
        // Context is passed to underlying serializer
    });

    it('handles deserialization context', function () {
        $json = '{"#type":"user","name":"John Doe","email":"<EMAIL>","createdAt":"2024-01-01T00:00:00+00:00"}';

        $user = $this->serializer->deserialize($json, ['groups' => ['public']]);

        expect($user)->toBeInstanceOf(User::class);
        // Context is passed to underlying serializer
    });

    it('throws SerializerException on serialization error', function () {
        // Create a circular reference
        $user = new User('John', '<EMAIL>');
        $profile = new Profile('John', 'Doe', 30);
        $user->setProfile($profile);

        // This should work normally, but let's test with invalid data
        $resource = fopen('php://memory', 'r');

        expect(fn () => $this->serializer->serialize($resource))
            ->toThrow(SerializerException::class);

        fclose($resource);
    });

    it('throws SerializerException on deserialization error', function () {
        $invalidJson = '{"invalid": json}';

        expect(fn () => $this->serializer->deserialize($invalidJson))
            ->toThrow(SerializerException::class);
    });

    it('handles empty objects', function () {
        $emptyObject = new stdClass();

        $json = $this->serializer->serialize($emptyObject);
        $result = $this->serializer->deserialize($json);

        expect($result)->toBeInstanceOf(stdClass::class);
    });

    it('preserves type information in serialized data', function () {
        $user = new User('John Doe', '<EMAIL>');

        $json = $this->serializer->serialize($user);
        $data = json_decode($json, true);

        expect($data)->toHaveKey('#type');
        expect($data['#type'])->toBe('user');
    });
});
