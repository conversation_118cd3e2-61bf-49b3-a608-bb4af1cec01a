<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Serializer\BuiltInObjectTypeDenormalizer;
use Vuryss\DoctrineLazyJsonOdm\Serializer\SerializerInterface;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Profile;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapper;
use Vuryss\Serializer\Denormalizer;
use Vuryss\Serializer\Metadata\BuiltInType;
use Vuryss\Serializer\Metadata\DataType;
use Vuryss\Serializer\Path;

describe('BuiltInObjectTypeDenormalizer', function () {
    beforeEach(function () {
        $this->typeMapper = new TypeMapper([
            'user' => User::class,
            'profile' => Profile::class,
        ]);
        $this->denormalizer = new BuiltInObjectTypeDenormalizer($this->typeMapper);
        $this->mockDenormalizer = Mockery::mock(Denormalizer::class);
        $this->mockPath = Mockery::mock(Path::class);
        $this->mockDataType = new DataType(BuiltInType::OBJECT, 'stdClass');
    });

    afterEach(function () {
        Mockery::close();
    });

    it('can be instantiated', function () {
        expect($this->denormalizer)->toBeInstanceOf(BuiltInObjectTypeDenormalizer::class);
    });

    it('supports denormalization of arrays with type key', function () {
        $data = [
            SerializerInterface::TYPE_KEY => 'user',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ];

        expect($this->denormalizer->supportsDenormalization($data, $this->mockDataType))->toBeTrue();
    });

    it('does not support denormalization of arrays without type key', function () {
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ];

        expect($this->denormalizer->supportsDenormalization($data, $this->mockDataType))->toBeFalse();
    });

    it('does not support denormalization of non-arrays', function () {
        expect($this->denormalizer->supportsDenormalization('string', $this->mockDataType))->toBeFalse();
        expect($this->denormalizer->supportsDenormalization(123, $this->mockDataType))->toBeFalse();
        expect($this->denormalizer->supportsDenormalization(null, $this->mockDataType))->toBeFalse();
    });

    it('does not support denormalization when type key is not string', function () {
        $data = [
            SerializerInterface::TYPE_KEY => 123,
            'name' => 'John Doe',
        ];

        expect($this->denormalizer->supportsDenormalization($data, $this->mockDataType))->toBeFalse();
    });

    it('denormalizes object using type mapper', function () {
        $data = [
            SerializerInterface::TYPE_KEY => 'user',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ];

        $expectedUser = new User('John Doe', '<EMAIL>');

        $this->mockDenormalizer
            ->shouldReceive('denormalize')
            ->once()
            ->with(
                ['name' => 'John Doe', 'email' => '<EMAIL>'], // Type key removed
                Mockery::on(function (DataType $dataType) {
                    return User::class === $dataType->getClassName();
                }),
                Mockery::any(),
                $this->mockPath,
                []
            )
            ->andReturn($expectedUser);

        $result = $this->denormalizer->denormalize(
            $data,
            $this->mockDataType,
            $this->mockDenormalizer,
            $this->mockPath
        );

        expect($result)->toBe($expectedUser);
    });

    it('removes type key from data before denormalization', function () {
        $data = [
            SerializerInterface::TYPE_KEY => 'user',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'other' => 'value',
        ];

        $this->mockDenormalizer
            ->shouldReceive('denormalize')
            ->once()
            ->with(
                ['name' => 'John Doe', 'email' => '<EMAIL>', 'other' => 'value'],
                Mockery::any(),
                Mockery::any(),
                Mockery::any(),
                Mockery::any()
            )
            ->andReturn(new User('John Doe', '<EMAIL>'));

        $this->denormalizer->denormalize(
            $data,
            $this->mockDataType,
            $this->mockDenormalizer,
            $this->mockPath
        );
    });

    it('uses full class name when no alias is configured', function () {
        $typeMapper = new TypeMapper([]); // No mappings
        $denormalizer = new BuiltInObjectTypeDenormalizer($typeMapper);

        $data = [
            SerializerInterface::TYPE_KEY => User::class,
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ];

        $this->mockDenormalizer
            ->shouldReceive('denormalize')
            ->once()
            ->with(
                ['name' => 'John Doe', 'email' => '<EMAIL>'],
                Mockery::on(function (DataType $dataType) {
                    return User::class === $dataType->getClassName();
                }),
                Mockery::any(),
                Mockery::any(),
                Mockery::any()
            )
            ->andReturn(new User('John Doe', '<EMAIL>'));

        $denormalizer->denormalize(
            $data,
            $this->mockDataType,
            $this->mockDenormalizer,
            $this->mockPath
        );
    });

    it('passes context to underlying denormalizer', function () {
        $data = [
            SerializerInterface::TYPE_KEY => 'user',
            'name' => 'John Doe',
        ];
        $context = ['groups' => ['public']];

        $this->mockDenormalizer
            ->shouldReceive('denormalize')
            ->once()
            ->with(
                ['name' => 'John Doe'],
                Mockery::any(),
                Mockery::any(),
                Mockery::any(),
                $context
            )
            ->andReturn(new User('John Doe', '<EMAIL>'));

        $this->denormalizer->denormalize(
            $data,
            $this->mockDataType,
            $this->mockDenormalizer,
            $this->mockPath,
            $context
        );
    });

    it('throws exception when type key is missing', function () {
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ];

        expect(fn () => $this->denormalizer->denormalize(
            $data,
            $this->mockDataType,
            $this->mockDenormalizer,
            $this->mockPath
        ))->toThrow(RuntimeException::class, 'Missing type key');
    });

    it('throws exception when type key is not string', function () {
        $data = [
            SerializerInterface::TYPE_KEY => 123,
            'name' => 'John Doe',
        ];

        expect(fn () => $this->denormalizer->denormalize(
            $data,
            $this->mockDataType,
            $this->mockDenormalizer,
            $this->mockPath
        ))->toThrow(RuntimeException::class, 'Missing type key');
    });

    it('creates correct DataType for denormalization', function () {
        $data = [
            SerializerInterface::TYPE_KEY => 'profile',
            'firstName' => 'John',
            'lastName' => 'Doe',
        ];

        $this->mockDenormalizer
            ->shouldReceive('denormalize')
            ->once()
            ->with(
                Mockery::any(),
                Mockery::on(function (DataType $dataType) {
                    return BuiltInType::OBJECT === $dataType->getBuiltInType()
                        && Profile::class === $dataType->getClassName();
                }),
                Mockery::any(),
                Mockery::any(),
                Mockery::any()
            )
            ->andReturn(new Profile('John', 'Doe', 30));

        $this->denormalizer->denormalize(
            $data,
            $this->mockDataType,
            $this->mockDenormalizer,
            $this->mockPath
        );
    });

    it('handles unknown types gracefully', function () {
        $data = [
            SerializerInterface::TYPE_KEY => 'unknown_type',
            'property' => 'value',
        ];

        $this->mockDenormalizer
            ->shouldReceive('denormalize')
            ->once()
            ->with(
                ['property' => 'value'],
                Mockery::on(function (DataType $dataType) {
                    return 'unknown_type' === $dataType->getClassName();
                }),
                Mockery::any(),
                Mockery::any(),
                Mockery::any()
            )
            ->andReturn(new stdClass());

        $result = $this->denormalizer->denormalize(
            $data,
            $this->mockDataType,
            $this->mockDenormalizer,
            $this->mockPath
        );

        expect($result)->toBeInstanceOf(stdClass::class);
    });
});
