<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Serializer\SerializerException;

describe('SerializerException', function () {
    it('can be instantiated with message only', function () {
        $exception = new SerializerException('Test message');

        expect($exception)->toBeInstanceOf(SerializerException::class);
        expect($exception)->toBeInstanceOf(RuntimeException::class);
        expect($exception->getMessage())->toBe('Test message');
        expect($exception->getCode())->toBe(0);
        expect($exception->getPrevious())->toBeNull();
    });

    it('can be instantiated with message and code', function () {
        $exception = new SerializerException('Test message', 123);

        expect($exception->getMessage())->toBe('Test message');
        expect($exception->getCode())->toBe(123);
        expect($exception->getPrevious())->toBeNull();
    });

    it('can be instantiated with message, code and previous exception', function () {
        $previous = new Exception('Previous exception');
        $exception = new SerializerException('Test message', 123, $previous);

        expect($exception->getMessage())->toBe('Test message');
        expect($exception->getCode())->toBe(123);
        expect($exception->getPrevious())->toBe($previous);
    });

    it('can be thrown and caught', function () {
        expect(fn () => throw new SerializerException('Test'))
            ->toThrow(SerializerException::class, 'Test');
    });

    it('can be caught as RuntimeException', function () {
        expect(fn () => throw new SerializerException('Test'))
            ->toThrow(RuntimeException::class, 'Test');
    });

    it('preserves stack trace', function () {
        try {
            throw new SerializerException('Test message');
        } catch (SerializerException $e) {
            expect($e->getTrace())->toBeArray();
            expect($e->getTrace())->not->toBeEmpty();
            expect($e->getFile())->toContain('SerializerExceptionTest.php');
            expect($e->getLine())->toBeInt();
        }
    });

    it('can chain exceptions', function () {
        $original = new InvalidArgumentException('Original error');
        $serializer = new SerializerException('Serialization failed', 0, $original);

        expect($serializer->getPrevious())->toBe($original);
        expect($serializer->getPrevious()->getMessage())->toBe('Original error');
    });

    it('supports string conversion', function () {
        $exception = new SerializerException('Test message', 123);
        $string = (string) $exception;

        expect($string)->toContain('SerializerException');
        expect($string)->toContain('Test message');
        expect($string)->toContain('123');
    });
});
