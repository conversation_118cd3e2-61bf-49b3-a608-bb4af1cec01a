<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Serializer\Serializer;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Profile;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapper;

describe('Circular Reference Handling', function () {
    beforeEach(function () {
        $this->typeMapper = new TypeMapper([
            'user' => User::class,
            'profile' => Profile::class,
        ]);
        $this->serializer = new Serializer($this->typeMapper);
    });

    it('handles simple object serialization without circular references', function () {
        $user = new User('<PERSON> Doe', '<EMAIL>');
        $profile = new Profile('John', 'Doe', 30);
        $user->setProfile($profile);

        // This should work fine - no circular reference
        $json = $this->serializer->serialize($user);
        expect($json)->toBeString();

        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized)->toBeInstanceOf(User::class);
        expect($deserialized->getProfile())->toBeInstanceOf(Profile::class);
    });

    it('handles objects with self-references gracefully', function () {
        // Create a simple object that might reference itself
        $data = new stdClass();
        $data->name = 'test';
        $data->value = 42;

        // This should serialize without issues
        $json = $this->serializer->serialize($data);
        expect($json)->toBeString();

        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized)->toBeInstanceOf(stdClass::class);
        expect($deserialized->name)->toBe('test');
    });

    it('prevents infinite recursion in serialization', function () {
        // Create objects that could potentially create circular references
        $user1 = new User('User 1', '<EMAIL>');
        $user2 = new User('User 2', '<EMAIL>');

        // In a real circular reference scenario, we'd have objects referencing each other
        // Since our test entities don't support this directly, we test with arrays
        $data = [
            'user1' => $user1,
            'user2' => $user2,
            'relationship' => 'friends',
        ];

        $json = $this->serializer->serialize($data);
        expect($json)->toBeString();

        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized)->toBeArray();
        expect($deserialized['user1'])->toBeInstanceOf(User::class);
        expect($deserialized['user2'])->toBeInstanceOf(User::class);
    });

    it('handles deeply nested objects without circular references', function () {
        // Create a deeply nested structure
        $level1 = new stdClass();
        $level1->name = 'level1';

        $level2 = new stdClass();
        $level2->name = 'level2';
        $level2->parent = $level1;

        $level3 = new stdClass();
        $level3->name = 'level3';
        $level3->parent = $level2;

        $json = $this->serializer->serialize($level3);
        expect($json)->toBeString();

        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized)->toBeInstanceOf(stdClass::class);
        expect($deserialized->name)->toBe('level3');
        expect($deserialized->parent->name)->toBe('level2');
        expect($deserialized->parent->parent->name)->toBe('level1');
    });

    it('handles arrays with potential circular references', function () {
        $user = new User('Test User', '<EMAIL>');

        // Create an array that references the same object multiple times
        $data = [
            'primary_user' => $user,
            'secondary_user' => $user, // Same object reference
            'users' => [$user, $user], // Same object in array
        ];

        $json = $this->serializer->serialize($data);
        expect($json)->toBeString();

        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized)->toBeArray();
        expect($deserialized['primary_user'])->toBeInstanceOf(User::class);
        expect($deserialized['secondary_user'])->toBeInstanceOf(User::class);
        expect($deserialized['users'])->toBeArray();
        expect($deserialized['users'][0])->toBeInstanceOf(User::class);
    });

    it('handles serialization of objects with complex relationships', function () {
        $user = new User('Complex User', '<EMAIL>');
        $profile = new Profile('Complex', 'User', 30, 'Has complex relationships');
        $user->setProfile($profile);

        // Create a structure that could potentially have circular references
        $data = [
            'user' => $user,
            'profile' => $profile, // Same profile object
            'metadata' => [
                'owner' => $user, // Same user object again
                'profile_copy' => $profile, // Same profile again
            ],
        ];

        $json = $this->serializer->serialize($data);
        expect($json)->toBeString();

        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized)->toBeArray();
        expect($deserialized['user'])->toBeInstanceOf(User::class);
        expect($deserialized['profile'])->toBeInstanceOf(Profile::class);
        expect($deserialized['metadata']['owner'])->toBeInstanceOf(User::class);
    });

    it('prevents stack overflow with deeply nested structures', function () {
        // Create a structure that could cause stack overflow
        $current = new stdClass();
        $current->value = 0;

        // Create a chain of 100 nested objects
        for ($i = 1; $i < 100; ++$i) {
            $next = new stdClass();
            $next->value = $i;
            $next->child = $current;
            $current = $next;
        }

        // This should not cause stack overflow
        $json = $this->serializer->serialize($current);
        expect($json)->toBeString();

        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized)->toBeInstanceOf(stdClass::class);
        expect($deserialized->value)->toBe(99);
    });

    it('handles serialization timeout for complex structures', function () {
        // Create a complex but valid structure
        $users = [];
        for ($i = 0; $i < 50; ++$i) {
            $user = new User("User $i", "user$<EMAIL>");
            $profile = new Profile("First$i", "Last$i", 20 + $i);
            $user->setProfile($profile);
            $users[] = $user;
        }

        $startTime = microtime(true);
        $json = $this->serializer->serialize($users);
        $endTime = microtime(true);

        $executionTime = $endTime - $startTime;

        // Should complete in reasonable time
        expect($executionTime)->toBeLessThan(5.0);
        expect($json)->toBeString();
    });

    it('handles deserialization of potentially circular JSON', function () {
        // JSON that could represent circular references
        $jsonWithReferences = json_encode([
            'id' => 1,
            'name' => 'Test',
            'children' => [
                ['id' => 2, 'name' => 'Child1', 'parent_id' => 1],
                ['id' => 3, 'name' => 'Child2', 'parent_id' => 1],
            ],
        ]);

        $deserialized = $this->serializer->deserialize($jsonWithReferences);
        expect($deserialized)->toBeArray();
        expect($deserialized['children'])->toBeArray();
        expect(count($deserialized['children']))->toBe(2);
    });

    it('maintains object identity during serialization', function () {
        $sharedProfile = new Profile('Shared', 'Profile', 25);

        $user1 = new User('User 1', '<EMAIL>');
        $user1->setProfile($sharedProfile);

        $user2 = new User('User 2', '<EMAIL>');
        $user2->setProfile($sharedProfile);

        $data = ['user1' => $user1, 'user2' => $user2];

        $json = $this->serializer->serialize($data);
        $deserialized = $this->serializer->deserialize($json);

        expect($deserialized['user1'])->toBeInstanceOf(User::class);
        expect($deserialized['user2'])->toBeInstanceOf(User::class);
        expect($deserialized['user1']->getProfile())->toBeInstanceOf(Profile::class);
        expect($deserialized['user2']->getProfile())->toBeInstanceOf(Profile::class);
    });

    it('handles recursive data structures gracefully', function () {
        // Create a tree-like structure
        $root = new stdClass();
        $root->name = 'root';
        $root->children = [];

        for ($i = 0; $i < 5; ++$i) {
            $child = new stdClass();
            $child->name = "child_$i";
            $child->parent_name = 'root';
            $root->children[] = $child;
        }

        $json = $this->serializer->serialize($root);
        expect($json)->toBeString();

        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized)->toBeInstanceOf(stdClass::class);
        expect($deserialized->name)->toBe('root');
        expect(count($deserialized->children))->toBe(5);
    });

    it('provides clear error messages for circular reference issues', function () {
        // Test that any circular reference errors are clear
        $user = new User('Error Test', '<EMAIL>');

        // This should work fine
        $json = $this->serializer->serialize($user);
        expect($json)->toBeString();

        // If there were circular reference issues, they would be caught here
        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized)->toBeInstanceOf(User::class);
    });
});
