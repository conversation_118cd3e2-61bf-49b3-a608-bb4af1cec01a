<?php

declare(strict_types=1);

use Doctrine\DBAL\Platforms\SqlitePlatform;
use Vuryss\DoctrineLazyJsonOdm\Serializer\Serializer;
use Vuryss\DoctrineLazyJsonOdm\Serializer\SerializerException;
use Vuryss\DoctrineLazyJsonOdm\Type\LazyJsonDocumentType;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapper;

describe('Malformed JSON Handling', function () {
    beforeEach(function () {
        $this->typeMapper = new TypeMapper();
        $this->serializer = new Serializer($this->typeMapper);
        $this->doctrineType = new LazyJsonDocumentType();
        $this->doctrineType->setSerializer($this->serializer);
        $this->doctrineType->setTypeMapper($this->typeMapper);
        $this->platform = new SqlitePlatform();
    });

    it('throws exception for invalid JSON syntax', function () {
        $invalidJson = '{"invalid": json}';

        expect(fn () => $this->serializer->deserialize($invalidJson))
            ->toThrow(SerializerException::class);
    });

    it('throws exception for incomplete JSON objects', function () {
        $incompleteJson = '{"name": "John", "email":';

        expect(fn () => $this->serializer->deserialize($incompleteJson))
            ->toThrow(SerializerException::class);
    });

    it('throws exception for malformed JSON arrays', function () {
        $malformedArray = '[{"name": "John"}, {"name": "Jane",}]';

        expect(fn () => $this->serializer->deserialize($malformedArray))
            ->toThrow(SerializerException::class);
    });

    it('handles empty JSON strings gracefully', function () {
        $result = $this->doctrineType->convertToPHPValue('', $this->platform);
        expect($result)->toBeNull();
    });

    it('handles null JSON values correctly', function () {
        $result = $this->serializer->deserialize('null');
        expect($result)->toBeNull();

        $result = $this->doctrineType->convertToPHPValue(null, $this->platform);
        expect($result)->toBeNull();
    });

    it('throws exception for JSON with invalid escape sequences', function () {
        $invalidEscape = '{"name": "John\xDoe"}';

        expect(fn () => $this->serializer->deserialize($invalidEscape))
            ->toThrow(SerializerException::class);
    });

    it('handles JSON with missing type information', function () {
        $jsonWithoutType = '{"name": "John", "email": "<EMAIL>"}';

        // Should deserialize as generic data
        $result = $this->serializer->deserialize($jsonWithoutType);
        expect($result)->toBeArray();
        expect($result['name'])->toBe('John');
    });

    it('handles JSON with invalid type information', function () {
        $jsonWithInvalidType = '{"#type": 123, "name": "John"}';

        // Should handle gracefully
        $result = $this->serializer->deserialize($jsonWithInvalidType);
        expect($result)->toBeArray();
    });

    it('throws exception for deeply nested malformed JSON', function () {
        $deeplyNested = '{"level1": {"level2": {"level3": {"invalid": json}}}}';

        expect(fn () => $this->serializer->deserialize($deeplyNested))
            ->toThrow(SerializerException::class);
    });

    it('handles JSON with unexpected data types', function () {
        $unexpectedTypes = '{"date": "not-a-date", "number": "not-a-number"}';

        // Should deserialize but may not convert types correctly
        $result = $this->serializer->deserialize($unexpectedTypes);
        expect($result)->toBeArray();
        expect($result['date'])->toBe('not-a-date');
        expect($result['number'])->toBe('not-a-number');
    });

    it('handles JSON with circular reference indicators', function () {
        $circularJson = '{"#type": "user", "name": "John", "self": {"$ref": "#"}}';

        // Should handle without infinite recursion
        $result = $this->serializer->deserialize($circularJson);
        expect($result)->toBeArray();
    });

    it('throws exception for JSON exceeding nesting limits', function () {
        // Create deeply nested JSON
        $deepJson = str_repeat('{"nested":', 1000).'"value"'.str_repeat('}', 1000);

        expect(fn () => $this->serializer->deserialize($deepJson))
            ->toThrow(SerializerException::class);
    });

    it('handles JSON with invalid UTF-8 sequences', function () {
        $invalidUtf8 = '{"name": "John\xFF\xFE"}';

        expect(fn () => $this->serializer->deserialize($invalidUtf8))
            ->toThrow(SerializerException::class);
    });

    it('handles JSON with control characters', function () {
        $controlChars = '{"name": "John\u0000Doe"}';

        // Should handle control characters appropriately
        $result = $this->serializer->deserialize($controlChars);
        expect($result)->toBeArray();
    });

    it('throws exception for JSON with unmatched brackets', function () {
        $unmatchedBrackets = '{"name": "John", "data": [1, 2, 3}';

        expect(fn () => $this->serializer->deserialize($unmatchedBrackets))
            ->toThrow(SerializerException::class);
    });

    it('handles JSON with duplicate keys', function () {
        $duplicateKeys = '{"name": "John", "name": "Jane"}';

        // Should handle duplicate keys (typically last value wins)
        $result = $this->serializer->deserialize($duplicateKeys);
        expect($result)->toBeArray();
        expect($result['name'])->toBe('Jane');
    });

    it('throws exception for JSON with trailing commas', function () {
        $trailingComma = '{"name": "John", "email": "<EMAIL>",}';

        expect(fn () => $this->serializer->deserialize($trailingComma))
            ->toThrow(SerializerException::class);
    });

    it('handles empty JSON objects and arrays', function () {
        $emptyObject = $this->serializer->deserialize('{}');
        expect($emptyObject)->toBeArray();
        expect($emptyObject)->toBeEmpty();

        $emptyArray = $this->serializer->deserialize('[]');
        expect($emptyArray)->toBeArray();
        expect($emptyArray)->toBeEmpty();
    });

    it('throws exception for JSON with invalid number formats', function () {
        $invalidNumber = '{"value": 123.45.67}';

        expect(fn () => $this->serializer->deserialize($invalidNumber))
            ->toThrow(SerializerException::class);
    });

    it('handles JSON with very large numbers', function () {
        $largeNumber = '{"value": 999999999999999999999999999999}';

        // Should handle large numbers appropriately
        $result = $this->serializer->deserialize($largeNumber);
        expect($result)->toBeArray();
        expect($result['value'])->toBeNumeric();
    });

    it('throws exception for JSON with invalid boolean values', function () {
        $invalidBoolean = '{"active": True}'; // Should be lowercase

        expect(fn () => $this->serializer->deserialize($invalidBoolean))
            ->toThrow(SerializerException::class);
    });

    it('handles JSON with mixed array types', function () {
        $mixedArray = '[1, "string", true, null, {"object": "value"}]';

        $result = $this->serializer->deserialize($mixedArray);
        expect($result)->toBeArray();
        expect($result[0])->toBe(1);
        expect($result[1])->toBe('string');
        expect($result[2])->toBe(true);
        expect($result[3])->toBeNull();
        expect($result[4])->toBeArray();
    });

    it('provides meaningful error messages for malformed JSON', function () {
        $invalidJson = '{"name": "John", "email":}';

        try {
            $this->serializer->deserialize($invalidJson);
            expect(false)->toBeTrue('Should have thrown exception');
        } catch (SerializerException $e) {
            expect($e->getMessage())->toBeString();
            expect($e->getMessage())->not->toBeEmpty();
        }
    });
});
