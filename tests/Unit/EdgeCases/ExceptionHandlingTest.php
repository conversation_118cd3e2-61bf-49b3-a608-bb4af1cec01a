<?php

declare(strict_types=1);

use Doctrine\DBAL\Platforms\SqlitePlatform;
use Vuryss\DoctrineLazyJsonOdm\Lazy\LazyJsonArray;
use Vuryss\DoctrineLazyJsonOdm\Serializer\Serializer;
use Vuryss\DoctrineLazyJsonOdm\Serializer\SerializerException;
use Vuryss\DoctrineLazyJsonOdm\Type\LazyJsonDocumentType;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapper;

describe('Exception Handling', function () {
    beforeEach(function () {
        $this->typeMapper = new TypeMapper();
        $this->serializer = new Serializer($this->typeMapper);
        $this->doctrineType = new LazyJsonDocumentType();
        $this->doctrineType->setSerializer($this->serializer);
        $this->doctrineType->setTypeMapper($this->typeMapper);
        $this->platform = new SqlitePlatform();
    });

    it('throws SerializerException for unserializable resources', function () {
        $resource = fopen('php://memory', 'r');

        expect(fn () => $this->serializer->serialize($resource))
            ->toThrow(SerializerException::class);

        fclose($resource);
    });

    it('throws SerializerException for invalid JSON in deserialization', function () {
        $invalidJson = '{"name": "John", "email":}';

        expect(fn () => $this->serializer->deserialize($invalidJson))
            ->toThrow(SerializerException::class);
    });

    it('provides meaningful error messages in exceptions', function () {
        $invalidJson = '{"invalid": json}';

        try {
            $this->serializer->deserialize($invalidJson);
            expect(false)->toBeTrue('Should have thrown exception');
        } catch (SerializerException $e) {
            expect($e->getMessage())->toBeString();
            expect($e->getMessage())->not->toBeEmpty();
            expect($e)->toBeInstanceOf(RuntimeException::class);
        }
    });

    it('preserves original exception in SerializerException', function () {
        $invalidJson = '{"name": "John", "email":}';

        try {
            $this->serializer->deserialize($invalidJson);
            expect(false)->toBeTrue('Should have thrown exception');
        } catch (SerializerException $e) {
            expect($e->getPrevious())->not->toBeNull();
            expect($e->getCode())->toBeInt();
        }
    });

    it('handles exceptions in lazy array operations', function () {
        $lazyArray = new LazyJsonArray([]);

        // Test read-only exceptions
        expect(fn () => $lazyArray[0] = 'value')
            ->toThrow(RuntimeException::class, 'The array is read-only');

        expect(function () use ($lazyArray) { unset($lazyArray[0]); })
            ->toThrow(RuntimeException::class, 'The array is read-only');
    });

    it('handles type conversion exceptions gracefully', function () {
        // Test with invalid type information
        $jsonWithInvalidType = '{"#type": null, "name": "John"}';

        // Should handle gracefully without throwing
        $result = $this->serializer->deserialize($jsonWithInvalidType);
        expect($result)->toBeArray();
    });

    it('handles missing class exceptions in type mapping', function () {
        $typeMapper = new TypeMapper(['nonexistent' => 'NonExistent\\Class']);
        $serializer = new Serializer($typeMapper);

        $jsonWithNonExistentType = '{"#type": "nonexistent", "name": "John"}';

        // Should handle gracefully or throw appropriate exception
        expect(fn () => $serializer->deserialize($jsonWithNonExistentType))
            ->toThrow(SerializerException::class);
    });

    it('handles memory exhaustion gracefully', function () {
        // Create a very large object that might cause memory issues
        $largeData = [];

        // Don't actually exhaust memory in tests, just test the pattern
        for ($i = 0; $i < 1000; ++$i) {
            $largeData["key_$i"] = str_repeat('x', 1000);
        }

        // This should work without memory exhaustion
        $json = $this->serializer->serialize($largeData);
        expect($json)->toBeString();

        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized)->toBeArray();
    });

    it('handles timeout scenarios in serialization', function () {
        // Create complex but manageable data
        $complexData = [];
        for ($i = 0; $i < 100; ++$i) {
            $complexData[] = [
                'id' => $i,
                'data' => str_repeat("content_$i ", 100),
                'nested' => [
                    'level1' => ['level2' => ['level3' => "deep_$i"]],
                ],
            ];
        }

        $startTime = microtime(true);
        $json = $this->serializer->serialize($complexData);
        $endTime = microtime(true);

        $executionTime = $endTime - $startTime;

        // Should complete in reasonable time
        expect($executionTime)->toBeLessThan(5.0);
        expect($json)->toBeString();
    });

    it('handles corrupted data gracefully', function () {
        // Test with various corrupted JSON scenarios
        $corruptedJsons = [
            '{"name": "John"', // Missing closing brace
            '{"name": "John",}', // Trailing comma
            '{"name": John"}', // Missing quotes
            '{name: "John"}', // Missing quotes on key
            '{"name": "John\xFF"}', // Invalid UTF-8
        ];

        foreach ($corruptedJsons as $corruptedJson) {
            expect(fn () => $this->serializer->deserialize($corruptedJson))
                ->toThrow(SerializerException::class);
        }
    });

    it('handles stack overflow prevention', function () {
        // Create deeply nested structure that could cause stack overflow
        $deepStructure = [];
        $current = &$deepStructure;

        for ($i = 0; $i < 500; ++$i) {
            $current['level'] = $i;
            $current['next'] = [];
            $current = &$current['next'];
        }
        $current['end'] = true;

        // Should handle without stack overflow
        $json = $this->serializer->serialize($deepStructure);
        expect($json)->toBeString();

        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized)->toBeArray();
    });

    it('handles invalid Doctrine type operations', function () {
        // Test with invalid platform
        $invalidPlatform = null;

        expect(fn () => $this->doctrineType->convertToDatabaseValue('test', $invalidPlatform))
            ->toThrow(TypeError::class);
    });

    it('handles serialization of invalid objects', function () {
        // Test with objects that might cause serialization issues
        $invalidObjects = [
            new class {
                private $resource;

                public function __construct()
                {
                    $this->resource = fopen('php://memory', 'r');
                }

                public function __destruct()
                {
                    if (is_resource($this->resource)) {
                        fclose($this->resource);
                    }
                }
            },
        ];

        foreach ($invalidObjects as $obj) {
            expect(fn () => $this->serializer->serialize($obj))
                ->toThrow(SerializerException::class);
        }
    });

    it('handles concurrent exception scenarios', function () {
        // Test that exceptions in one operation don't affect others
        $validData = ['valid' => 'data'];
        $invalidJson = '{"invalid": json}';

        // Valid operation should work
        $json = $this->serializer->serialize($validData);
        expect($json)->toBeString();

        // Invalid operation should throw
        expect(fn () => $this->serializer->deserialize($invalidJson))
            ->toThrow(SerializerException::class);

        // Valid operation should still work after exception
        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized)->toBe($validData);
    });

    it('handles exception chaining correctly', function () {
        $invalidJson = '{"name": "John", "email":}';

        try {
            $this->serializer->deserialize($invalidJson);
            expect(false)->toBeTrue('Should have thrown exception');
        } catch (SerializerException $e) {
            // Should have previous exception
            expect($e->getPrevious())->not->toBeNull();

            // Should be proper exception chain
            expect($e)->toBeInstanceOf(SerializerException::class);
            expect($e)->toBeInstanceOf(RuntimeException::class);
        }
    });

    it('handles error recovery scenarios', function () {
        // Test that serializer can recover from errors
        $invalidJson = '{"invalid": json}';
        $validData = ['valid' => 'data'];

        // First operation fails
        try {
            $this->serializer->deserialize($invalidJson);
        } catch (SerializerException $e) {
            // Expected
        }

        // Subsequent operations should work
        $json = $this->serializer->serialize($validData);
        expect($json)->toBeString();

        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized)->toBe($validData);
    });

    it('provides proper exception context', function () {
        $resource = fopen('php://memory', 'r');

        try {
            $this->serializer->serialize($resource);
            expect(false)->toBeTrue('Should have thrown exception');
        } catch (SerializerException $e) {
            expect($e->getMessage())->toBeString();
            expect($e->getFile())->toContain('ExceptionHandlingTest.php');
            expect($e->getLine())->toBeInt();
            expect($e->getTrace())->toBeArray();
        } finally {
            fclose($resource);
        }
    });

    it('handles edge case exception scenarios', function () {
        // Test various edge cases that might cause exceptions
        $edgeCases = [
            '', // Empty string
            '   ', // Whitespace only
            'null', // String "null"
            'undefined', // Invalid value
            '[]', // Empty array
            '{}', // Empty object
        ];

        foreach ($edgeCases as $case) {
            // These should either work or throw SerializerException
            try {
                $result = $this->serializer->deserialize($case);
                expect($result)->not->toBeNull(); // If it works, should return something
            } catch (SerializerException $e) {
                expect($e)->toBeInstanceOf(SerializerException::class);
            }
        }
    });
});
