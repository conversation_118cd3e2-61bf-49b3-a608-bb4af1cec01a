<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Lazy\LazyJsonArray;
use Vuryss\DoctrineLazyJsonOdm\Serializer\Serializer;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Address;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Profile;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapper;

describe('Large Dataset Handling', function () {
    beforeEach(function () {
        $this->typeMapper = new TypeMapper([
            'user' => User::class,
            'profile' => Profile::class,
            'address' => Address::class,
        ]);
        $this->serializer = new Serializer($this->typeMapper);
    });

    it('handles large arrays efficiently', function () {
        $largeArray = [];
        for ($i = 0; $i < 10000; ++$i) {
            $largeArray[] = new User("User $i", "user$<EMAIL>");
        }

        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        $json = $this->serializer->serialize($largeArray);

        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        $executionTime = $endTime - $startTime;
        $memoryUsed = $endMemory - $startMemory;

        expect($executionTime)->toBeLessThan(10.0); // Should complete in under 10 seconds
        expect($memoryUsed)->toBeLessThan(500 * 1024 * 1024); // Less than 500MB
        expect(strlen($json))->toBeGreaterThan(0);
    });

    it('handles large object deserialization efficiently', function () {
        // Create a large dataset
        $users = [];
        for ($i = 0; $i < 1000; ++$i) {
            $users[] = new User("User $i", "user$<EMAIL>");
        }

        $json = $this->serializer->serialize($users);

        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        $deserialized = $this->serializer->deserialize($json);

        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        $executionTime = $endTime - $startTime;
        $memoryUsed = $endMemory - $startMemory;

        expect($executionTime)->toBeLessThan(5.0);
        expect($memoryUsed)->toBeLessThan(200 * 1024 * 1024); // Less than 200MB
        expect($deserialized)->toBeArray();
        expect(count($deserialized))->toBe(1000);
    });

    it('handles very large strings in objects', function () {
        $largeString = str_repeat('Large content ', 10000); // ~130KB string
        $profile = new Profile('John', 'Doe', 30, $largeString);

        $startTime = microtime(true);
        $json = $this->serializer->serialize($profile);
        $endTime = microtime(true);

        $executionTime = $endTime - $startTime;

        expect($executionTime)->toBeLessThan(1.0);
        expect(strlen($json))->toBeGreaterThan(100000); // Should be large

        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized)->toBeInstanceOf(Profile::class);
        expect($deserialized->bio)->toBe($largeString);
    });

    it('handles deeply nested object structures', function () {
        // Create deeply nested structure
        $current = new stdClass();
        $current->level = 0;
        $current->data = 'leaf';

        for ($i = 1; $i < 1000; ++$i) {
            $next = new stdClass();
            $next->level = $i;
            $next->child = $current;
            $current = $next;
        }

        $startTime = microtime(true);
        $json = $this->serializer->serialize($current);
        $endTime = microtime(true);

        $executionTime = $endTime - $startTime;

        expect($executionTime)->toBeLessThan(2.0);
        expect($json)->toBeString();

        $deserialized = $this->serializer->deserialize($json);
        expect($deserialized)->toBeInstanceOf(stdClass::class);
        expect($deserialized->level)->toBe(999);
    });

    it('handles large lazy arrays efficiently', function () {
        $largeDataset = [];
        for ($i = 0; $i < 5000; ++$i) {
            $largeDataset[] = new User("User $i", "user$<EMAIL>");
        }

        $startTime = microtime(true);
        $lazyArray = new LazyJsonArray($largeDataset);
        $endTime = microtime(true);

        $creationTime = $endTime - $startTime;

        expect($creationTime)->toBeLessThan(1.0);
        expect($lazyArray->count())->toBe(5000);

        // Test random access performance
        $startTime = microtime(true);
        for ($i = 0; $i < 100; ++$i) {
            $randomIndex = rand(0, 4999);
            $user = $lazyArray[$randomIndex];
            expect($user->getName())->toBe("User $randomIndex");
        }
        $endTime = microtime(true);

        $accessTime = $endTime - $startTime;
        expect($accessTime)->toBeLessThan(0.1);
    });

    it('handles memory pressure gracefully', function () {
        // Create multiple large objects to test memory handling
        $datasets = [];

        for ($j = 0; $j < 5; ++$j) {
            $dataset = [];
            for ($i = 0; $i < 1000; ++$i) {
                $address = new Address(
                    str_repeat("Street $i ", 10),
                    str_repeat("City $i ", 5),
                    '12345',
                    'Country'
                );
                $profile = new Profile("First$i", "Last$i", 25, str_repeat("Bio $i ", 20), $address);
                $user = new User("User $i", "user$<EMAIL>");
                $user->setProfile($profile);
                $dataset[] = $user;
            }
            $datasets[] = $dataset;
        }

        $startMemory = memory_get_usage();

        // Serialize all datasets
        $serializedData = [];
        foreach ($datasets as $dataset) {
            $serializedData[] = $this->serializer->serialize($dataset);
        }

        $endMemory = memory_get_usage();
        $memoryUsed = $endMemory - $startMemory;

        expect($memoryUsed)->toBeLessThan(1024 * 1024 * 1024); // Less than 1GB
        expect(count($serializedData))->toBe(5);
    });

    it('handles concurrent large operations', function () {
        // Simulate concurrent operations on large datasets
        $dataset1 = [];
        $dataset2 = [];

        for ($i = 0; $i < 1000; ++$i) {
            $dataset1[] = new User("Dataset1 User $i", "d1user$<EMAIL>");
            $dataset2[] = new User("Dataset2 User $i", "d2user$<EMAIL>");
        }

        $startTime = microtime(true);

        // Simulate concurrent serialization
        $json1 = $this->serializer->serialize($dataset1);
        $json2 = $this->serializer->serialize($dataset2);

        // Simulate concurrent deserialization
        $deserialized1 = $this->serializer->deserialize($json1);
        $deserialized2 = $this->serializer->deserialize($json2);

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        expect($executionTime)->toBeLessThan(5.0);
        expect(count($deserialized1))->toBe(1000);
        expect(count($deserialized2))->toBe(1000);
        expect($deserialized1[0]->getName())->toContain('Dataset1');
        expect($deserialized2[0]->getName())->toContain('Dataset2');
    });

    it('handles extremely large JSON strings', function () {
        // Create a very large JSON string
        $largeData = [];
        for ($i = 0; $i < 10000; ++$i) {
            $largeData["key_$i"] = str_repeat("value_$i ", 10);
        }

        $startTime = microtime(true);
        $json = $this->serializer->serialize($largeData);
        $endTime = microtime(true);

        $serializationTime = $endTime - $startTime;

        expect($serializationTime)->toBeLessThan(3.0);
        expect(strlen($json))->toBeGreaterThan(1000000); // Should be > 1MB

        $startTime = microtime(true);
        $deserialized = $this->serializer->deserialize($json);
        $endTime = microtime(true);

        $deserializationTime = $endTime - $startTime;

        expect($deserializationTime)->toBeLessThan(3.0);
        expect($deserialized)->toBeArray();
        expect(count($deserialized))->toBe(10000);
    });

    it('maintains performance with repeated large operations', function () {
        $dataset = [];
        for ($i = 0; $i < 500; ++$i) {
            $dataset[] = new User("User $i", "user$<EMAIL>");
        }

        $times = [];

        // Perform multiple serialization/deserialization cycles
        for ($cycle = 0; $cycle < 10; ++$cycle) {
            $startTime = microtime(true);

            $json = $this->serializer->serialize($dataset);
            $deserialized = $this->serializer->deserialize($json);

            $endTime = microtime(true);
            $times[] = $endTime - $startTime;

            expect(count($deserialized))->toBe(500);
        }

        // Performance should be consistent
        $avgTime = array_sum($times) / count($times);
        $maxTime = max($times);
        $minTime = min($times);

        expect($avgTime)->toBeLessThan(2.0);
        expect($maxTime - $minTime)->toBeLessThan(1.0); // Variance should be low
    });

    it('handles edge case array sizes', function () {
        // Test various edge case sizes
        $sizes = [0, 1, 2, 10, 100, 1000, 10000];

        foreach ($sizes as $size) {
            $dataset = [];
            for ($i = 0; $i < $size; ++$i) {
                $dataset[] = new User("User $i", "user$<EMAIL>");
            }

            $startTime = microtime(true);
            $json = $this->serializer->serialize($dataset);
            $deserialized = $this->serializer->deserialize($json);
            $endTime = microtime(true);

            $executionTime = $endTime - $startTime;

            expect($executionTime)->toBeLessThan(5.0, "Size $size took too long");
            expect(count($deserialized))->toBe($size);
        }
    });

    it('handles memory cleanup after large operations', function () {
        $initialMemory = memory_get_usage();

        // Perform large operation
        $largeDataset = [];
        for ($i = 0; $i < 5000; ++$i) {
            $largeDataset[] = new User("User $i", "user$<EMAIL>");
        }

        $json = $this->serializer->serialize($largeDataset);
        $deserialized = $this->serializer->deserialize($json);

        // Clear references
        unset($largeDataset, $json, $deserialized);

        // Force garbage collection
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        $finalMemory = memory_get_usage();
        $memoryIncrease = $finalMemory - $initialMemory;

        // Memory increase should be reasonable
        expect($memoryIncrease)->toBeLessThan(50 * 1024 * 1024); // Less than 50MB permanent increase
    });
});
