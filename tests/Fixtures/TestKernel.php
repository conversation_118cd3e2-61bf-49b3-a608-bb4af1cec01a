<?php

declare(strict_types=1);

namespace Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures;

use Doctrine\Bundle\DoctrineBundle\DoctrineBundle;
use Symfony\Bundle\FrameworkBundle\FrameworkBundle;
use Symfony\Component\Config\Loader\LoaderInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\HttpKernel\Kernel;
use Vuryss\DoctrineLazyJsonOdm\DoctrineLazyJsonOdmBundle;

class TestKernel extends Kernel
{
    public function registerBundles(): iterable
    {
        return [
            new FrameworkBundle(),
            new DoctrineBundle(),
            new DoctrineLazyJsonOdmBundle(),
        ];
    }

    public function registerContainerConfiguration(LoaderInterface $loader): void
    {
        $loader->load(function (ContainerBuilder $container): void {
            $container->loadFromExtension('framework', [
                'test' => true,
                'secret' => 'test-secret',
                'cache' => [
                    'app' => 'cache.adapter.array',
                ],
            ]);

            $container->loadFromExtension('doctrine', [
                'dbal' => [
                    'driver' => 'pdo_sqlite',
                    'path' => ':memory:',
                    'charset' => 'utf8',
                ],
                'orm' => [
                    'auto_generate_proxy_classes' => true,
                    'auto_mapping' => true,
                    'mappings' => [
                        'Test' => [
                            'type' => 'attribute',
                            'dir' => __DIR__.'/Entity',
                            'prefix' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity',
                            'is_bundle' => false,
                        ],
                    ],
                ],
            ]);

            $container->loadFromExtension('doctrine_json_odm', [
                'type_map' => [
                    'user' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\User',
                    'profile' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Profile',
                    'address' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Address',
                    'product' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Product',
                    'product_specs' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\ProductSpecifications',
                    'product_variant' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\ProductVariant',
                    'category' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Category',
                    'order' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Order',
                    'order_item' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\OrderItem',
                    'order_shipping' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\OrderShipping',
                    'order_payment' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\OrderPayment',
                    'user_preference' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\UserPreference',
                ],
            ]);

            $container->setParameter('kernel.cache_dir', sys_get_temp_dir().'/doctrine_lazy_json_odm_test');
            $container->setParameter('kernel.logs_dir', sys_get_temp_dir().'/doctrine_lazy_json_odm_test/logs');
        });
    }

    public function getCacheDir(): string
    {
        return sys_get_temp_dir().'/doctrine_lazy_json_odm_test/cache/'.$this->environment;
    }

    public function getLogDir(): string
    {
        return sys_get_temp_dir().'/doctrine_lazy_json_odm_test/logs';
    }
}
