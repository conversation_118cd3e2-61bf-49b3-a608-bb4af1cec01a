<?php

declare(strict_types=1);

namespace Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity;

readonly class Category
{
    public function __construct(
        public string $name,
        public string $slug,
        public ?string $description = null,
        public ?Category $parent = null,
        public array $metadata = [],
    ) {
    }

    public function isTopLevel(): bool
    {
        return null === $this->parent;
    }

    public function getPath(): string
    {
        if (null === $this->parent) {
            return $this->slug;
        }

        return $this->parent->getPath().'/'.$this->slug;
    }
}
