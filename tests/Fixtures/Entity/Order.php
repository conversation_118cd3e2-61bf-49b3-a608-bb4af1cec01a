<?php

declare(strict_types=1);

namespace Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity;

use Doctrine\ORM\Mapping as ORM;
use Vuryss\DoctrineLazyJsonOdm\Lazy\LazyJsonArray;
use Vuryss\DoctrineLazyJsonOdm\Type\LazyJsonDocumentType;

#[ORM\Entity(repositoryClass: OrderRepository::class)]
#[ORM\Table(name: 'orders')]
class Order
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 255, unique: true)]
    private string $orderNumber;

    #[ORM\Column(type: 'integer')]
    private int $userId;

    #[ORM\Column(type: 'decimal', precision: 10, scale: 2)]
    private string $totalAmount;

    #[ORM\Column(type: 'string', length: 50)]
    private string $status;

    #[ORM\Column(type: LazyJsonDocumentType::NAME, nullable: true)]
    private ?LazyJsonArray $items = null;

    #[ORM\Column(type: LazyJsonDocumentType::NAME, nullable: true)]
    private ?OrderShipping $shipping = null;

    #[ORM\Column(type: LazyJsonDocumentType::NAME, nullable: true)]
    private ?OrderPayment $payment = null;

    #[ORM\Column(type: 'datetime_immutable')]
    private \DateTimeImmutable $createdAt;

    #[ORM\Column(type: 'datetime_immutable', nullable: true)]
    private ?\DateTimeImmutable $shippedAt = null;

    public function __construct(string $orderNumber, int $userId, string $totalAmount, string $status = 'pending')
    {
        $this->orderNumber = $orderNumber;
        $this->userId = $userId;
        $this->totalAmount = $totalAmount;
        $this->status = $status;
        $this->createdAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getOrderNumber(): string
    {
        return $this->orderNumber;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function getTotalAmount(): string
    {
        return $this->totalAmount;
    }

    public function setTotalAmount(string $totalAmount): void
    {
        $this->totalAmount = $totalAmount;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): void
    {
        $this->status = $status;
    }

    public function getItems(): ?LazyJsonArray
    {
        return $this->items;
    }

    public function setItems(?LazyJsonArray $items): void
    {
        $this->items = $items;
    }

    public function getShipping(): ?OrderShipping
    {
        return $this->shipping;
    }

    public function setShipping(?OrderShipping $shipping): void
    {
        $this->shipping = $shipping;
    }

    public function getPayment(): ?OrderPayment
    {
        return $this->payment;
    }

    public function setPayment(?OrderPayment $payment): void
    {
        $this->payment = $payment;
    }

    public function getCreatedAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getShippedAt(): ?\DateTimeImmutable
    {
        return $this->shippedAt;
    }

    public function setShippedAt(?\DateTimeImmutable $shippedAt): void
    {
        $this->shippedAt = $shippedAt;
    }
}
