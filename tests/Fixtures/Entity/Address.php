<?php

declare(strict_types=1);

namespace Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity;

readonly class Address
{
    public function __construct(
        public string $street,
        public string $city,
        public string $postalCode,
        public string $country,
        public ?string $state = null,
    ) {
    }

    public function getFormattedAddress(): string
    {
        $parts = [$this->street, $this->city];

        if ($this->state) {
            $parts[] = $this->state;
        }

        $parts[] = $this->postalCode;
        $parts[] = $this->country;

        return implode(', ', $parts);
    }
}
