<?php

declare(strict_types=1);

namespace Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Product>
 */
class ProductRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Product::class);
    }

    /**
     * @return Product[]
     */
    public function findActiveProducts(): array
    {
        return $this->findBy(['active' => true]);
    }

    /**
     * @return Product[]
     */
    public function findProductsWithSpecifications(): array
    {
        return $this->createQueryBuilder('p')
            ->where('p.specifications IS NOT NULL')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Product[]
     */
    public function findProductsWithVariants(): array
    {
        return $this->createQueryBuilder('p')
            ->where('p.variants IS NOT NULL')
            ->getQuery()
            ->getResult();
    }

    public function save(Product $product, bool $flush = false): void
    {
        $this->getEntityManager()->persist($product);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Product $product, bool $flush = false): void
    {
        $this->getEntityManager()->remove($product);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
