<?php

declare(strict_types=1);

namespace Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity;

use Doctrine\ORM\Mapping as ORM;
use Vuryss\DoctrineLazyJsonOdm\Lazy\LazyJsonArray;
use Vuryss\DoctrineLazyJsonOdm\Type\LazyJsonDocumentType;

#[ORM\Entity(repositoryClass: ProductRepository::class)]
#[ORM\Table(name: 'products')]
class Product
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 255)]
    private string $name;

    #[ORM\Column(type: 'string', length: 500, nullable: true)]
    private ?string $description = null;

    #[ORM\Column(type: 'decimal', precision: 10, scale: 2)]
    private string $price;

    #[ORM\Column(type: LazyJsonDocumentType::NAME, nullable: true)]
    private ?ProductSpecifications $specifications = null;

    #[ORM\Column(type: LazyJsonDocumentType::NAME, nullable: true)]
    private ?LazyJsonArray $categories = null;

    #[ORM\Column(type: LazyJsonDocumentType::NAME, nullable: true)]
    private ?LazyJsonArray $variants = null;

    #[ORM\Column(type: 'boolean')]
    private bool $active = true;

    #[ORM\Column(type: 'datetime_immutable')]
    private \DateTimeImmutable $createdAt;

    public function __construct(string $name, string $price)
    {
        $this->name = $name;
        $this->price = $price;
        $this->createdAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getPrice(): string
    {
        return $this->price;
    }

    public function setPrice(string $price): void
    {
        $this->price = $price;
    }

    public function getSpecifications(): ?ProductSpecifications
    {
        return $this->specifications;
    }

    public function setSpecifications(?ProductSpecifications $specifications): void
    {
        $this->specifications = $specifications;
    }

    public function getCategories(): ?LazyJsonArray
    {
        return $this->categories;
    }

    public function setCategories(?LazyJsonArray $categories): void
    {
        $this->categories = $categories;
    }

    public function getVariants(): ?LazyJsonArray
    {
        return $this->variants;
    }

    public function setVariants(?LazyJsonArray $variants): void
    {
        $this->variants = $variants;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): void
    {
        $this->active = $active;
    }

    public function getCreatedAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }
}
