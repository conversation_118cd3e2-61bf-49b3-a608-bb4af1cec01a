<?php

declare(strict_types=1);

namespace Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity;

readonly class OrderItem
{
    public function __construct(
        public int $productId,
        public string $productName,
        public int $quantity,
        public string $unitPrice,
        public string $totalPrice,
        public ?array $productOptions = null,
    ) {
    }

    public function getSubtotal(): string
    {
        return bcmul($this->unitPrice, (string) $this->quantity, 2);
    }
}
