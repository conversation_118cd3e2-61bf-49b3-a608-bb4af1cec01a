<?php

declare(strict_types=1);

namespace Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity;

readonly class ProductVariant
{
    public function __construct(
        public string $name,
        public string $sku,
        public string $price,
        public int $stock,
        public array $attributes = [],
        public bool $active = true,
    ) {
    }

    public function isInStock(): bool
    {
        return $this->stock > 0;
    }

    public function getAttribute(string $key): mixed
    {
        return $this->attributes[$key] ?? null;
    }
}
