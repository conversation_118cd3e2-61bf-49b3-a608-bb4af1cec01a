<?php

declare(strict_types=1);

namespace Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity;

readonly class Profile
{
    public function __construct(
        public string $firstName,
        public string $lastName,
        public int $age,
        public ?string $bio = null,
        public ?Address $address = null,
        public array $socialLinks = [],
    ) {
    }

    public function getFullName(): string
    {
        return $this->firstName.' '.$this->lastName;
    }
}
