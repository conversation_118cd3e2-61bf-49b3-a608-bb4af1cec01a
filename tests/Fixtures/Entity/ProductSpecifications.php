<?php

declare(strict_types=1);

namespace Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity;

readonly class ProductSpecifications
{
    public function __construct(
        public array $dimensions = [],
        public ?string $weight = null,
        public ?string $material = null,
        public ?string $color = null,
        public array $features = [],
        public array $technicalSpecs = [],
    ) {
    }

    public function hasFeature(string $feature): bool
    {
        return in_array($feature, $this->features, true);
    }

    public function getTechnicalSpec(string $key): mixed
    {
        return $this->technicalSpecs[$key] ?? null;
    }
}
