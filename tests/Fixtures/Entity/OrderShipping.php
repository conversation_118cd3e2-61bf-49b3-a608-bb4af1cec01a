<?php

declare(strict_types=1);

namespace Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity;

readonly class OrderShipping
{
    public function __construct(
        public Address $address,
        public string $method,
        public string $cost,
        public ?string $trackingNumber = null,
        public ?string $carrier = null,
        public ?\DateTimeImmutable $estimatedDelivery = null,
    ) {
    }

    public function isTracked(): bool
    {
        return null !== $this->trackingNumber;
    }
}
