<?php

declare(strict_types=1);

namespace Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity;

readonly class UserPreference
{
    public function __construct(
        public string $key,
        public mixed $value,
        public string $type = 'string',
        public ?\DateTimeImmutable $updatedAt = null,
    ) {
    }

    public function getValue(): mixed
    {
        return match ($this->type) {
            'boolean' => (bool) $this->value,
            'integer' => (int) $this->value,
            'float' => (float) $this->value,
            'array' => is_array($this->value) ? $this->value : json_decode($this->value, true),
            default => $this->value,
        };
    }
}
