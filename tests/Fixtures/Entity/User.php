<?php

declare(strict_types=1);

namespace Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity;

use Doctrine\ORM\Mapping as ORM;
use Vuryss\DoctrineLazyJsonOdm\Lazy\LazyJsonArray;
use Vuryss\DoctrineLazyJsonOdm\Type\LazyJsonDocumentType;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\Table(name: 'users')]
class User
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 255)]
    private string $name;

    #[ORM\Column(type: 'string', length: 255, unique: true)]
    private string $email;

    #[ORM\Column(type: LazyJsonDocumentType::NAME, nullable: true)]
    private ?Profile $profile = null;

    #[ORM\Column(type: LazyJsonDocumentType::NAME, nullable: true)]
    private ?LazyJsonArray $orders = null;

    #[ORM\Column(type: LazyJsonDocumentType::NAME, nullable: true)]
    private ?LazyJsonArray $preferences = null;

    #[ORM\Column(type: 'datetime_immutable')]
    private \DateTimeImmutable $createdAt;

    public function __construct(string $name, string $email)
    {
        $this->name = $name;
        $this->email = $email;
        $this->createdAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): void
    {
        $this->email = $email;
    }

    public function getProfile(): ?Profile
    {
        return $this->profile;
    }

    public function setProfile(?Profile $profile): void
    {
        $this->profile = $profile;
    }

    public function getOrders(): ?LazyJsonArray
    {
        return $this->orders;
    }

    public function setOrders(?LazyJsonArray $orders): void
    {
        $this->orders = $orders;
    }

    public function getPreferences(): ?LazyJsonArray
    {
        return $this->preferences;
    }

    public function setPreferences(?LazyJsonArray $preferences): void
    {
        $this->preferences = $preferences;
    }

    public function getCreatedAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }
}
