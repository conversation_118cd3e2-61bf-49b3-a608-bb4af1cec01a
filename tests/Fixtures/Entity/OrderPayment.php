<?php

declare(strict_types=1);

namespace Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity;

readonly class OrderPayment
{
    public function __construct(
        public string $method,
        public string $status,
        public string $amount,
        public ?string $transactionId = null,
        public ?string $gatewayResponse = null,
        public ?\DateTimeImmutable $processedAt = null,
    ) {
    }

    public function isSuccessful(): bool
    {
        return 'completed' === $this->status;
    }

    public function isPending(): bool
    {
        return 'pending' === $this->status;
    }
}
