<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Lazy\LazyJsonArray;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Order;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\OrderItem;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Product;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\ProductSpecifications;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Profile;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;

describe('Repository Functionality', function () {
    beforeEach(function () {
        // Create test data
        // Create users with profiles
        $profile1 = new Profile('John', 'Doe', 30, 'Software Developer');
        $user1 = new User('John Doe', '<EMAIL>');
        $user1->setProfile($profile1);

        $profile2 = new Profile('<PERSON>', '<PERSON>', 28, 'Designer');
        $user2 = new User('<PERSON>', '<EMAIL>');
        $user2->setProfile($profile2);

        // Create user with orders
        $orderItems = [
            new OrderItem(1, 'Laptop', 1, '999.99', '999.99'),
            new OrderItem(2, 'Mouse', 1, '29.99', '29.99'),
        ];
        $user1->setOrders(new LazyJsonArray($orderItems));

        // Create products with specifications
        $specs1 = new ProductSpecifications(
            dimensions: ['width' => 30, 'height' => 20],
            weight: '2kg',
            material: 'Aluminum'
        );
        $product1 = new Product('Laptop Pro', '1299.99');
        $product1->setSpecifications($specs1);

        $product2 = new Product('Simple Mouse', '19.99');
        // No specifications for this product

        // Create orders
        $order1 = new Order('ORD-001', $user1->getId() ?? 1, '1029.98', 'pending');
        $order2 = new Order('ORD-002', $user2->getId() ?? 2, '299.99', 'completed');

        // Persist all entities
        $this->persistAndFlush($user1, $user2, $product1, $product2, $order1, $order2);
    });

    it('finds users with profiles using custom repository method', function () {
        $usersWithProfiles = $this->getUserRepository()->findUsersWithProfiles();

        expect($usersWithProfiles)->toBeArray();
        expect(count($usersWithProfiles))->toBeGreaterThan(0);

        foreach ($usersWithProfiles as $user) {
            expect($user)->toBeInstanceOf(User::class);
            expect($user->getProfile())->not->toBeNull();
            expect($user->getProfile())->toBeInstanceOf(Profile::class);
        }
    });

    it('finds users with orders using custom repository method', function () {
        $usersWithOrders = $this->getUserRepository()->findUsersWithOrders();

        expect($usersWithOrders)->toBeArray();
        expect(count($usersWithOrders))->toBeGreaterThan(0);

        foreach ($usersWithOrders as $user) {
            expect($user)->toBeInstanceOf(User::class);
            expect($user->getOrders())->not->toBeNull();
            expect($user->getOrders())->toBeInstanceOf(LazyJsonArray::class);
        }
    });

    it('finds products with specifications using custom repository method', function () {
        $productsWithSpecs = $this->getProductRepository()->findProductsWithSpecifications();

        expect($productsWithSpecs)->toBeArray();
        expect(count($productsWithSpecs))->toBeGreaterThan(0);

        foreach ($productsWithSpecs as $product) {
            expect($product)->toBeInstanceOf(Product::class);
            expect($product->getSpecifications())->not->toBeNull();
            expect($product->getSpecifications())->toBeInstanceOf(ProductSpecifications::class);
        }
    });

    it('finds orders by user ID', function () {
        $user = $this->getUserRepository()->findByEmail('<EMAIL>');
        $orders = $this->getOrderRepository()->findByUserId($user->getId());

        expect($orders)->toBeArray();
        expect(count($orders))->toBeGreaterThan(0);

        foreach ($orders as $order) {
            expect($order)->toBeInstanceOf(Order::class);
            expect($order->getUserId())->toBe($user->getId());
        }
    });

    it('finds orders with shipping information', function () {
        $ordersWithShipping = $this->getOrderRepository()->findOrdersWithShipping();

        expect($ordersWithShipping)->toBeArray();

        foreach ($ordersWithShipping as $order) {
            expect($order)->toBeInstanceOf(Order::class);
            expect($order->getShipping())->not->toBeNull();
        }
    });

    it('finds orders with payment information', function () {
        $ordersWithPayment = $this->getOrderRepository()->findOrdersWithPayment();

        expect($ordersWithPayment)->toBeArray();

        foreach ($ordersWithPayment as $order) {
            expect($order)->toBeInstanceOf(Order::class);
            expect($order->getPayment())->not->toBeNull();
        }
    });

    it('finds active products', function () {
        $activeProducts = $this->getProductRepository()->findActiveProducts();

        expect($activeProducts)->toBeArray();
        expect(count($activeProducts))->toBeGreaterThan(0);

        foreach ($activeProducts as $product) {
            expect($product)->toBeInstanceOf(Product::class);
            expect($product->isActive())->toBeTrue();
        }
    });

    it('finds orders by status', function () {
        $pendingOrders = $this->getOrderRepository()->findByStatus('pending');
        $completedOrders = $this->getOrderRepository()->findByStatus('completed');

        expect($pendingOrders)->toBeArray();
        expect($completedOrders)->toBeArray();

        foreach ($pendingOrders as $order) {
            expect($order->getStatus())->toBe('pending');
        }

        foreach ($completedOrders as $order) {
            expect($order->getStatus())->toBe('completed');
        }
    });

    it('saves and removes entities using repository methods', function () {
        $user = new User('Repository Test', '<EMAIL>');

        // Save using repository
        $this->getUserRepository()->save($user, true);

        $savedUser = $this->getUserRepository()->findByEmail('<EMAIL>');
        expect($savedUser)->not->toBeNull();
        expect($savedUser->getName())->toBe('Repository Test');

        // Remove using repository
        $this->getUserRepository()->remove($savedUser, true);

        $removedUser = $this->getUserRepository()->findByEmail('<EMAIL>');
        expect($removedUser)->toBeNull();
    });

    it('handles complex queries with JSON fields', function () {
        // This tests that JSON fields don't interfere with regular queries
        $allUsers = $this->getUserRepository()->findAll();
        $usersByEmail = $this->getUserRepository()->findBy(['email' => '<EMAIL>']);

        expect($allUsers)->toBeArray();
        expect(count($allUsers))->toBeGreaterThan(0);

        expect($usersByEmail)->toBeArray();
        expect(count($usersByEmail))->toBe(1);
        expect($usersByEmail[0]->getEmail())->toBe('<EMAIL>');
    });

    it('maintains performance with large result sets', function () {
        // Create additional test data
        for ($i = 0; $i < 50; ++$i) {
            $user = new User("Bulk User $i", "bulk$<EMAIL>");
            $profile = new Profile("First$i", "Last$i", 25 + ($i % 30));
            $user->setProfile($profile);
            $this->entityManager->persist($user);
        }
        $this->entityManager->flush();

        $startTime = microtime(true);

        $allUsers = $this->getUserRepository()->findAll();
        $usersWithProfiles = $this->getUserRepository()->findUsersWithProfiles();

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        expect($executionTime)->toBeLessThan(1.0); // Should be fast
        expect(count($allUsers))->toBeGreaterThan(50);
        expect(count($usersWithProfiles))->toBeGreaterThan(50);
    });

    it('handles repository inheritance correctly', function () {
        $userRepo = $this->getUserRepository();
        $productRepo = $this->getProductRepository();
        $orderRepo = $this->getOrderRepository();

        // All should be proper repository instances
        expect($userRepo)->toBeInstanceOf(Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository::class);
        expect($productRepo)->toBeInstanceOf(Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository::class);
        expect($orderRepo)->toBeInstanceOf(Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository::class);

        // Should have access to entity manager
        expect($userRepo->getEntityName())->toBe(User::class);
        expect($productRepo->getEntityName())->toBe(Product::class);
        expect($orderRepo->getEntityName())->toBe(Order::class);
    });

    it('supports custom query builder methods', function () {
        $queryBuilder = $this->getUserRepository()->createQueryBuilder('u');
        $query = $queryBuilder
            ->where('u.profile IS NOT NULL')
            ->getQuery();

        $usersWithProfiles = $query->getResult();

        expect($usersWithProfiles)->toBeArray();
        foreach ($usersWithProfiles as $user) {
            expect($user->getProfile())->not->toBeNull();
        }
    });

    it('handles pagination with JSON fields', function () {
        $queryBuilder = $this->getUserRepository()->createQueryBuilder('u');
        $query = $queryBuilder
            ->setFirstResult(0)
            ->setMaxResults(5)
            ->getQuery();

        $paginatedUsers = $query->getResult();

        expect($paginatedUsers)->toBeArray();
        expect(count($paginatedUsers))->toBeLessThanOrEqual(5);

        foreach ($paginatedUsers as $user) {
            expect($user)->toBeInstanceOf(User::class);
        }
    });
});
