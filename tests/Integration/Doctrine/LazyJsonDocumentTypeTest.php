<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Lazy\LazyJsonArray;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Address;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Profile;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;
use Vuryss\DoctrineLazyJsonOdm\Type\LazyJsonDocumentType;

describe('LazyJsonDocumentType Integration', function () {
    it('converts objects to database JSON format', function () {
        $address = new Address('123 Main St', 'New York', '10001', 'USA');
        $profile = new Profile('John', 'Doe', 30, 'Developer', $address);

        $databaseValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToDatabaseValue($profile, LazyJsonDocumentType::NAME);

        expect($databaseValue)->toBeString();

        $data = json_decode($databaseValue, true);
        expect($data)->toHaveKey('#type');
        expect($data['firstName'])->toBe('John');
        expect($data['address']['street'])->toBe('123 Main St');
    });

    it('converts database JSON to lazy objects', function () {
        $json = '{"#type":"profile","firstName":"John","lastName":"Doe","age":30,"bio":"Developer","address":{"#type":"address","street":"123 Main St","city":"New York","postalCode":"10001","country":"USA","state":null},"socialLinks":[]}';

        $phpValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToPHPValue($json, LazyJsonDocumentType::NAME);

        expect($phpValue)->toBeInstanceOf(Profile::class);
        expect($phpValue->firstName)->toBe('John');
        expect($phpValue->address)->toBeInstanceOf(Address::class);
    });

    it('handles arrays as lazy arrays', function () {
        $users = [
            new User('John Doe', '<EMAIL>'),
            new User('Jane Smith', '<EMAIL>'),
        ];

        $json = $this->serializer->serialize($users);

        $phpValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToPHPValue($json, LazyJsonDocumentType::NAME);

        expect($phpValue)->toBeInstanceOf(LazyJsonArray::class);
        expect($phpValue->count())->toBe(2);
        expect($phpValue[0])->toBeInstanceOf(User::class);
    });

    it('handles null values correctly', function () {
        $databaseValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToDatabaseValue(null, LazyJsonDocumentType::NAME);

        expect($databaseValue)->toBeNull();

        $phpValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToPHPValue(null, LazyJsonDocumentType::NAME);

        expect($phpValue)->toBeNull();
    });

    it('handles empty strings as null', function () {
        $phpValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToPHPValue('', LazyJsonDocumentType::NAME);

        expect($phpValue)->toBeNull();
    });

    it('preserves lazy array functionality after database round trip', function () {
        $users = [
            new User('John Doe', '<EMAIL>'),
            new User('Jane Smith', '<EMAIL>'),
        ];

        $lazyArray = new LazyJsonArray($users);

        // Convert to database and back
        $databaseValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToDatabaseValue($lazyArray, LazyJsonDocumentType::NAME);

        $phpValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToPHPValue($databaseValue, LazyJsonDocumentType::NAME);

        expect($phpValue)->toBeInstanceOf(LazyJsonArray::class);
        expect($phpValue->count())->toBe(2);
        expect($phpValue[0]->getName())->toBe('John Doe');
        expect($phpValue[1]->getName())->toBe('Jane Smith');
    });

    it('maintains object identity through serialization', function () {
        $profile = new Profile('John', 'Doe', 30);

        $databaseValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToDatabaseValue($profile, LazyJsonDocumentType::NAME);

        $phpValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToPHPValue($databaseValue, LazyJsonDocumentType::NAME);

        expect($phpValue)->toBeInstanceOf(Profile::class);
        expect($phpValue->firstName)->toBe($profile->firstName);
        expect($phpValue->lastName)->toBe($profile->lastName);
        expect($phpValue->age)->toBe($profile->age);
    });

    it('handles complex nested structures', function () {
        $address = new Address('123 Main St', 'New York', '10001', 'USA', 'NY');
        $profile = new Profile(
            'John',
            'Doe',
            30,
            'Software Developer',
            $address,
            ['github' => 'johndoe', 'linkedin' => 'john-doe']
        );

        $databaseValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToDatabaseValue($profile, LazyJsonDocumentType::NAME);

        $phpValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToPHPValue($databaseValue, LazyJsonDocumentType::NAME);

        expect($phpValue)->toBeInstanceOf(Profile::class);
        expect($phpValue->address)->toBeInstanceOf(Address::class);
        expect($phpValue->address->state)->toBe('NY');
        expect($phpValue->socialLinks)->toBe(['github' => 'johndoe', 'linkedin' => 'john-doe']);
    });

    it('uses type mapper for class resolution', function () {
        $user = new User('John Doe', '<EMAIL>');

        $databaseValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToDatabaseValue($user, LazyJsonDocumentType::NAME);

        $data = json_decode($databaseValue, true);
        expect($data['#type'])->toBe('user'); // Should use alias from type mapper

        $phpValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToPHPValue($databaseValue, LazyJsonDocumentType::NAME);

        expect($phpValue)->toBeInstanceOf(User::class);
    });

    it('handles DateTime objects correctly', function () {
        $user = new User('John Doe', '<EMAIL>');

        $databaseValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToDatabaseValue($user, LazyJsonDocumentType::NAME);

        $phpValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToPHPValue($databaseValue, LazyJsonDocumentType::NAME);

        expect($phpValue->getCreatedAt())->toBeInstanceOf(DateTimeImmutable::class);
        expect($phpValue->getCreatedAt()->format('Y-m-d'))->toBe($user->getCreatedAt()->format('Y-m-d'));
    });

    it('creates lazy proxies for objects with type metadata', function () {
        $profile = new Profile('John', 'Doe', 30);
        $json = $this->serializer->serialize($profile);

        // Manually test the lazy proxy creation
        $data = json_decode($json, true);
        expect($data)->toHaveKey('#type');

        $phpValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToPHPValue($json, LazyJsonDocumentType::NAME);

        expect($phpValue)->toBeInstanceOf(Profile::class);

        // Should be a lazy proxy (PHP 8.4 feature)
        // The object should work normally when accessed
        expect($phpValue->firstName)->toBe('John');
    });

    it('handles large datasets efficiently', function () {
        $users = [];
        for ($i = 0; $i < 100; ++$i) {
            $users[] = new User("User $i", "user$<EMAIL>");
        }

        $startTime = microtime(true);

        $databaseValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToDatabaseValue($users, LazyJsonDocumentType::NAME);

        $phpValue = $this->entityManager
            ->getConnection()
            ->getDatabasePlatform()
            ->convertToPHPValue($databaseValue, LazyJsonDocumentType::NAME);

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        expect($executionTime)->toBeLessThan(1.0); // Should be fast
        expect($phpValue)->toBeInstanceOf(LazyJsonArray::class);
        expect($phpValue->count())->toBe(100);
    });
});
