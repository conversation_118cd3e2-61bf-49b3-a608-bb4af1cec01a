<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Lazy\LazyJsonArray;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Address;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\OrderItem;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Product;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\ProductSpecifications;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Profile;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\UserPreference;

describe('Entity Persistence and Retrieval', function () {
    it('persists and retrieves user with profile', function () {
        $address = new Address('123 Main St', 'New York', '10001', 'USA');
        $profile = new Profile('<PERSON>', 'Doe', 30, 'Software Developer', $address);
        $user = new User('<PERSON> Doe', '<EMAIL>');
        $user->setProfile($profile);

        $this->persistAndFlush($user);
        $this->clear();

        $retrievedUser = $this->getUserRepository()->findByEmail('<EMAIL>');

        expect($retrievedUser)->not->toBeNull();
        expect($retrievedUser->getName())->toBe('John Doe');
        expect($retrievedUser->getProfile())->toBeInstanceOf(Profile::class);
        expect($retrievedUser->getProfile()->firstName)->toBe('John');
        expect($retrievedUser->getProfile()->address)->toBeInstanceOf(Address::class);
        expect($retrievedUser->getProfile()->address->street)->toBe('123 Main St');
    });

    it('persists and retrieves user with orders array', function () {
        $orderItems = [
            new OrderItem(1, 'Laptop', 1, '999.99', '999.99'),
            new OrderItem(2, 'Mouse', 2, '29.99', '59.98'),
        ];
        $orders = new LazyJsonArray($orderItems);

        $user = new User('Jane Smith', '<EMAIL>');
        $user->setOrders($orders);

        $this->persistAndFlush($user);
        $this->clear();

        $retrievedUser = $this->getUserRepository()->findByEmail('<EMAIL>');

        expect($retrievedUser)->not->toBeNull();
        expect($retrievedUser->getOrders())->toBeInstanceOf(LazyJsonArray::class);
        expect($retrievedUser->getOrders()->count())->toBe(2);
        expect($retrievedUser->getOrders()[0])->toBeInstanceOf(OrderItem::class);
        expect($retrievedUser->getOrders()[0]->productName)->toBe('Laptop');
        expect($retrievedUser->getOrders()[1]->quantity)->toBe(2);
    });

    it('persists and retrieves product with specifications', function () {
        $specs = new ProductSpecifications(
            dimensions: ['width' => 30, 'height' => 20, 'depth' => 5],
            weight: '2.5kg',
            material: 'Aluminum',
            color: 'Silver',
            features: ['waterproof', 'lightweight'],
            technicalSpecs: ['cpu' => 'Intel i7', 'ram' => '16GB']
        );

        $product = new Product('Laptop Pro', '1299.99');
        $product->setSpecifications($specs);

        $this->persistAndFlush($product);
        $this->clear();

        $retrievedProduct = $this->getProductRepository()->findOneBy(['name' => 'Laptop Pro']);

        expect($retrievedProduct)->not->toBeNull();
        expect($retrievedProduct->getSpecifications())->toBeInstanceOf(ProductSpecifications::class);
        expect($retrievedProduct->getSpecifications()->weight)->toBe('2.5kg');
        expect($retrievedProduct->getSpecifications()->features)->toBe(['waterproof', 'lightweight']);
        expect($retrievedProduct->getSpecifications()->technicalSpecs['cpu'])->toBe('Intel i7');
    });

    it('persists and retrieves user with preferences array', function () {
        $preferences = [
            new UserPreference('theme', 'dark', 'string'),
            new UserPreference('notifications', true, 'boolean'),
            new UserPreference('max_items', 50, 'integer'),
        ];

        $user = new User('Bob Wilson', '<EMAIL>');
        $user->setPreferences(new LazyJsonArray($preferences));

        $this->persistAndFlush($user);
        $this->clear();

        $retrievedUser = $this->getUserRepository()->findByEmail('<EMAIL>');

        expect($retrievedUser)->not->toBeNull();
        expect($retrievedUser->getPreferences())->toBeInstanceOf(LazyJsonArray::class);
        expect($retrievedUser->getPreferences()->count())->toBe(3);

        $themePreference = $retrievedUser->getPreferences()[0];
        expect($themePreference)->toBeInstanceOf(UserPreference::class);
        expect($themePreference->key)->toBe('theme');
        expect($themePreference->getValue())->toBe('dark');
    });

    it('handles null JSON fields correctly', function () {
        $user = new User('Simple User', '<EMAIL>');
        // Profile, orders, and preferences are null by default

        $this->persistAndFlush($user);
        $this->clear();

        $retrievedUser = $this->getUserRepository()->findByEmail('<EMAIL>');

        expect($retrievedUser)->not->toBeNull();
        expect($retrievedUser->getProfile())->toBeNull();
        expect($retrievedUser->getOrders())->toBeNull();
        expect($retrievedUser->getPreferences())->toBeNull();
    });

    it('updates JSON fields correctly', function () {
        $user = new User('Update Test', '<EMAIL>');
        $this->persistAndFlush($user);

        // Add profile
        $profile = new Profile('Update', 'Test', 25);
        $user->setProfile($profile);
        $this->persistAndFlush($user);
        $this->clear();

        $retrievedUser = $this->getUserRepository()->findByEmail('<EMAIL>');
        expect($retrievedUser->getProfile())->toBeInstanceOf(Profile::class);
        expect($retrievedUser->getProfile()->firstName)->toBe('Update');

        // Update profile
        $newProfile = new Profile('Updated', 'User', 26, 'New bio');
        $retrievedUser->setProfile($newProfile);
        $this->persistAndFlush($retrievedUser);
        $this->clear();

        $finalUser = $this->getUserRepository()->findByEmail('<EMAIL>');
        expect($finalUser->getProfile()->firstName)->toBe('Updated');
        expect($finalUser->getProfile()->bio)->toBe('New bio');
    });

    it('handles complex nested object updates', function () {
        $address = new Address('123 Main St', 'New York', '10001', 'USA');
        $profile = new Profile('John', 'Doe', 30, 'Developer', $address);
        $user = new User('Complex User', '<EMAIL>');
        $user->setProfile($profile);

        $this->persistAndFlush($user);
        $this->clear();

        $retrievedUser = $this->getUserRepository()->findByEmail('<EMAIL>');

        // Update nested address
        $newAddress = new Address('456 Oak Ave', 'Boston', '02101', 'USA', 'MA');
        $newProfile = new Profile(
            $retrievedUser->getProfile()->firstName,
            $retrievedUser->getProfile()->lastName,
            $retrievedUser->getProfile()->age,
            'Senior Developer',
            $newAddress
        );
        $retrievedUser->setProfile($newProfile);

        $this->persistAndFlush($retrievedUser);
        $this->clear();

        $finalUser = $this->getUserRepository()->findByEmail('<EMAIL>');
        expect($finalUser->getProfile()->bio)->toBe('Senior Developer');
        expect($finalUser->getProfile()->address->street)->toBe('456 Oak Ave');
        expect($finalUser->getProfile()->address->city)->toBe('Boston');
        expect($finalUser->getProfile()->address->state)->toBe('MA');
    });

    it('persists multiple entities with JSON fields', function () {
        $users = [];
        for ($i = 0; $i < 5; ++$i) {
            $profile = new Profile("First$i", "Last$i", 20 + $i);
            $user = new User("User $i", "user$<EMAIL>");
            $user->setProfile($profile);
            $users[] = $user;
        }

        foreach ($users as $user) {
            $this->entityManager->persist($user);
        }
        $this->entityManager->flush();
        $this->clear();

        $retrievedUsers = $this->getUserRepository()->findUsersWithProfiles();

        expect(count($retrievedUsers))->toBe(5);
        foreach ($retrievedUsers as $user) {
            expect($user->getProfile())->toBeInstanceOf(Profile::class);
        }
    });

    it('handles lazy loading of JSON fields', function () {
        $largeProfile = new Profile(
            'John',
            'Doe',
            30,
            str_repeat('Large bio content ', 100), // Large bio
            new Address(str_repeat('Long street ', 50), 'City', '12345', 'Country')
        );

        $user = new User('Lazy Test', '<EMAIL>');
        $user->setProfile($largeProfile);

        $this->persistAndFlush($user);
        $this->clear();

        $startMemory = memory_get_usage();

        // Retrieve user but don't access profile yet
        $retrievedUser = $this->getUserRepository()->findByEmail('<EMAIL>');

        $memoryAfterLoad = memory_get_usage();

        // Access profile (should trigger lazy loading)
        $profile = $retrievedUser->getProfile();
        $firstName = $profile->firstName;

        $memoryAfterAccess = memory_get_usage();

        expect($retrievedUser)->not->toBeNull();
        expect($firstName)->toBe('John');

        // Memory usage should be reasonable
        $loadMemory = $memoryAfterLoad - $startMemory;
        $accessMemory = $memoryAfterAccess - $memoryAfterLoad;

        expect($loadMemory)->toBeLessThan(5 * 1024 * 1024); // Less than 5MB for loading
    });

    it('maintains data integrity across multiple operations', function () {
        $user = new User('Integrity Test', '<EMAIL>');
        $this->persistAndFlush($user);

        // Multiple updates
        for ($i = 0; $i < 3; ++$i) {
            $this->clear();
            $retrievedUser = $this->getUserRepository()->findByEmail('<EMAIL>');

            $profile = new Profile("First$i", "Last$i", 25 + $i, "Bio $i");
            $retrievedUser->setProfile($profile);

            $this->persistAndFlush($retrievedUser);
        }

        $this->clear();
        $finalUser = $this->getUserRepository()->findByEmail('<EMAIL>');

        expect($finalUser->getProfile()->firstName)->toBe('First2');
        expect($finalUser->getProfile()->age)->toBe(27);
        expect($finalUser->getProfile()->bio)->toBe('Bio 2');
    });

    it('handles concurrent access patterns', function () {
        $user = new User('Concurrent Test', '<EMAIL>');
        $profile = new Profile('John', 'Doe', 30);
        $user->setProfile($profile);

        $this->persistAndFlush($user);
        $this->clear();

        // Simulate concurrent access
        $user1 = $this->getUserRepository()->findByEmail('<EMAIL>');
        $user2 = $this->getUserRepository()->findByEmail('<EMAIL>');

        expect($user1->getProfile()->firstName)->toBe('John');
        expect($user2->getProfile()->firstName)->toBe('John');

        // Both should work independently
        expect($user1->getId())->toBe($user2->getId());
        expect($user1->getProfile())->toBeInstanceOf(Profile::class);
        expect($user2->getProfile())->toBeInstanceOf(Profile::class);
    });
});
