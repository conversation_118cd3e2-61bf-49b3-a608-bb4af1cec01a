<?php

declare(strict_types=1);

namespace Vuryss\DoctrineLazyJsonOdm\Tests\Integration;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Tools\SchemaTool;
use PHPUnit\Framework\TestCase;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Vuryss\DoctrineLazyJsonOdm\Serializer\SerializerInterface;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Order;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\Product;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\TestKernel;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapperInterface;

abstract class IntegrationTestCase extends TestCase
{
    protected TestKernel $kernel;
    protected ContainerInterface $container;
    protected EntityManagerInterface $entityManager;
    protected SerializerInterface $serializer;
    protected TypeMapperInterface $typeMapper;

    protected function setUp(): void
    {
        $this->kernel = new TestKernel('test', true);
        $this->kernel->boot();

        $this->container = $this->kernel->getContainer();
        $this->entityManager = $this->container->get('doctrine.orm.entity_manager');
        $this->serializer = $this->container->get(SerializerInterface::class);
        $this->typeMapper = $this->container->get(TypeMapperInterface::class);

        $this->createSchema();
    }

    protected function tearDown(): void
    {
        $this->dropSchema();
        $this->kernel->shutdown();
    }

    private function createSchema(): void
    {
        $schemaTool = new SchemaTool($this->entityManager);
        $metadata = $this->entityManager->getMetadataFactory()->getAllMetadata();
        $schemaTool->createSchema($metadata);
    }

    private function dropSchema(): void
    {
        $schemaTool = new SchemaTool($this->entityManager);
        $metadata = $this->entityManager->getMetadataFactory()->getAllMetadata();
        $schemaTool->dropSchema($metadata);
    }

    protected function persistAndFlush(object ...$entities): void
    {
        foreach ($entities as $entity) {
            $this->entityManager->persist($entity);
        }
        $this->entityManager->flush();
    }

    protected function refresh(object $entity): void
    {
        $this->entityManager->refresh($entity);
    }

    protected function clear(): void
    {
        $this->entityManager->clear();
    }

    protected function getUserRepository()
    {
        return $this->entityManager->getRepository(User::class);
    }

    protected function getProductRepository()
    {
        return $this->entityManager->getRepository(Product::class);
    }

    protected function getOrderRepository()
    {
        return $this->entityManager->getRepository(Order::class);
    }
}
