<?php

declare(strict_types=1);

use Symfony\Component\DependencyInjection\ContainerBuilder;
use Vuryss\DoctrineLazyJsonOdm\DoctrineLazyJsonOdmBundle;
use Vuryss\DoctrineLazyJsonOdm\Serializer\Serializer;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapper;

describe('Bundle Configuration', function () {
    it('loads default configuration correctly', function () {
        // Test that bundle loads with minimal configuration
        expect($this->container->get('Vuryss\\DoctrineLazyJsonOdm\\TypeMapper\\TypeMapper'))->toBeInstanceOf(TypeMapper::class);
        expect($this->container->get('Vuryss\\DoctrineLazyJsonOdm\\Serializer\\Serializer'))->toBeInstanceOf(Serializer::class);
    });

    it('applies type map configuration', function () {
        $typeMapper = $this->container->get('Vuryss\\DoctrineLazyJsonOdm\\TypeMapper\\TypeMapper');

        // Test configured mappings from TestKernel
        expect($typeMapper->getClassByType('user'))->toBe('Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\User');
        expect($typeMapper->getClassByType('profile'))->toBe('Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Profile');
        expect($typeMapper->getClassByType('address'))->toBe('Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Address');
    });

    it('handles empty type map configuration', function () {
        // Create a container with empty type map
        $container = new ContainerBuilder();
        $bundle = new DoctrineLazyJsonOdmBundle();

        $config = ['type_map' => []];

        // Should not throw exception
        expect(fn () => $bundle->loadExtension($config, $this->createMockConfigurator($container), $container))
            ->not->toThrow();
    });

    it('validates type map configuration format', function () {
        // This test verifies the configuration structure is correct
        $typeMapper = $this->container->get('Vuryss\\DoctrineLazyJsonOdm\\TypeMapper\\TypeMapper');

        // All configured aliases should map to valid class names
        $testMappings = [
            'user' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\User',
            'profile' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Profile',
            'address' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Address',
        ];

        foreach ($testMappings as $alias => $className) {
            expect($typeMapper->getClassByType($alias))->toBe($className);
            expect($typeMapper->getTypeByClass($className))->toBe($alias);
        }
    });

    it('supports cache pool configuration', function () {
        // Test that cache pool configuration is handled
        $serializer = $this->container->get('Vuryss\\DoctrineLazyJsonOdm\\Serializer\\Serializer');

        // Serializer should be created successfully (cache pool is optional)
        expect($serializer)->toBeInstanceOf(Serializer::class);
    });

    it('provides configuration examples in definition', function () {
        $bundle = new DoctrineLazyJsonOdmBundle();

        // Test that bundle can be configured (this tests the configure method)
        expect($bundle)->toBeInstanceOf(DoctrineLazyJsonOdmBundle::class);

        // Bundle should have configuration definition
        $reflection = new ReflectionClass($bundle);
        expect($reflection->hasMethod('configure'))->toBeTrue();
    });

    it('handles complex type map configurations', function () {
        $typeMapper = $this->container->get('Vuryss\\DoctrineLazyJsonOdm\\TypeMapper\\TypeMapper');

        // Test various alias formats
        $complexMappings = [
            'product_specs' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\ProductSpecifications',
            'order_item' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\OrderItem',
            'user_preference' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\UserPreference',
        ];

        foreach ($complexMappings as $alias => $className) {
            expect($typeMapper->getClassByType($alias))->toBe($className);
        }
    });

    it('maintains configuration consistency across requests', function () {
        // Get type mapper multiple times
        $typeMapper1 = $this->container->get('Vuryss\\DoctrineLazyJsonOdm\\TypeMapper\\TypeMapper');
        $typeMapper2 = $this->container->get('Vuryss\\DoctrineLazyJsonOdm\\TypeMapper\\TypeMapper');

        // Should be the same instance (singleton)
        expect($typeMapper1)->toBe($typeMapper2);

        // Configuration should be consistent
        expect($typeMapper1->getClassByType('user'))->toBe($typeMapper2->getClassByType('user'));
    });

    it('supports namespace-based type aliases', function () {
        $typeMapper = $this->container->get('Vuryss\\DoctrineLazyJsonOdm\\TypeMapper\\TypeMapper');

        // Test that namespace-like aliases work
        $namespaceAliases = [
            'order_shipping' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\OrderShipping',
            'order_payment' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\OrderPayment',
        ];

        foreach ($namespaceAliases as $alias => $className) {
            expect($typeMapper->getClassByType($alias))->toBe($className);
        }
    });

    it('handles configuration inheritance correctly', function () {
        // Test that configuration is properly inherited and applied
        $bundle = new DoctrineLazyJsonOdmBundle();

        expect($bundle)->toBeInstanceOf(Symfony\Component\HttpKernel\Bundle\AbstractBundle::class);

        // Bundle should implement required methods
        $reflection = new ReflectionClass($bundle);
        expect($reflection->hasMethod('configure'))->toBeTrue();
        expect($reflection->hasMethod('loadExtension'))->toBeTrue();
        expect($reflection->hasMethod('boot'))->toBeTrue();
    });

    it('provides proper service configuration', function () {
        // Test that services are configured with correct arguments
        $typeMapper = $this->container->get('Vuryss\\DoctrineLazyJsonOdm\\TypeMapper\\TypeMapper');
        $serializer = $this->container->get('Vuryss\\DoctrineLazyJsonOdm\\Serializer\\Serializer');

        expect($typeMapper)->toBeInstanceOf(TypeMapper::class);
        expect($serializer)->toBeInstanceOf(Serializer::class);

        // Serializer should be configured with type mapper
        // This is tested indirectly by verifying serialization works with type aliases
        $testObject = new Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User('Test', '<EMAIL>');
        $json = $serializer->serialize($testObject);
        $data = json_decode($json, true);

        expect($data['#type'])->toBe('user'); // Should use alias from type mapper
    });

    it('supports environment-specific configuration', function () {
        // Test that configuration works in test environment
        expect($this->kernel->getEnvironment())->toBe('test');

        $typeMapper = $this->container->get('Vuryss\\DoctrineLazyJsonOdm\\TypeMapper\\TypeMapper');
        expect($typeMapper)->toBeInstanceOf(TypeMapper::class);

        // Configuration should be loaded correctly in test environment
        expect($typeMapper->getClassByType('user'))->toBe('Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\User');
    });

    it('handles configuration validation', function () {
        // Test that configuration validation works
        $bundle = new DoctrineLazyJsonOdmBundle();

        // Bundle should have proper configuration definition
        expect($bundle)->toBeInstanceOf(DoctrineLazyJsonOdmBundle::class);

        // Configuration should be validated (tested implicitly by successful loading)
        expect($this->container->get('Vuryss\\DoctrineLazyJsonOdm\\TypeMapper\\TypeMapper'))->not->toBeNull();
    });
});
