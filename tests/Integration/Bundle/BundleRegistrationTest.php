<?php

declare(strict_types=1);

use Doctrine\DBAL\Types\Type;
use Vuryss\DoctrineLazyJsonOdm\DoctrineLazyJsonOdmBundle;
use Vuryss\DoctrineLazyJsonOdm\Serializer\SerializerInterface;
use Vuryss\DoctrineLazyJsonOdm\Type\LazyJsonDocumentType;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapperInterface;

describe('Bundle Registration', function () {
    it('registers bundle correctly in kernel', function () {
        $bundles = $this->kernel->getBundles();

        expect($bundles)->toHaveKey('DoctrineLazyJsonOdmBundle');
        expect($bundles['DoctrineLazyJsonOdmBundle'])->toBeInstanceOf(DoctrineLazyJsonOdmBundle::class);
    });

    it('registers services in container', function () {
        expect($this->container->has(SerializerInterface::class))->toBeTrue();
        expect($this->container->has(TypeMapperInterface::class))->toBeTrue();

        $serializer = $this->container->get(SerializerInterface::class);
        $typeMapper = $this->container->get(TypeMapperInterface::class);

        expect($serializer)->toBeInstanceOf(SerializerInterface::class);
        expect($typeMapper)->toBeInstanceOf(TypeMapperInterface::class);
    });

    it('registers Doctrine type correctly', function () {
        expect(Type::hasType(LazyJsonDocumentType::NAME))->toBeTrue();

        $type = Type::getType(LazyJsonDocumentType::NAME);
        expect($type)->toBeInstanceOf(LazyJsonDocumentType::class);
    });

    it('configures type mapper with bundle configuration', function () {
        $typeMapper = $this->container->get(TypeMapperInterface::class);

        // Test configured type mappings
        expect($typeMapper->getClassByType('user'))->toBe('Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\User');
        expect($typeMapper->getClassByType('profile'))->toBe('Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Profile');
        expect($typeMapper->getClassByType('product'))->toBe('Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Product');

        // Test reverse mappings
        expect($typeMapper->getTypeByClass('Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\User'))->toBe('user');
        expect($typeMapper->getTypeByClass('Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Profile'))->toBe('profile');
    });

    it('provides public services for dependency injection', function () {
        // Services should be accessible for dependency injection
        expect($this->container->get(SerializerInterface::class))->not->toBeNull();
        expect($this->container->get(TypeMapperInterface::class))->not->toBeNull();

        // Services should be the same instance (singleton)
        $serializer1 = $this->container->get(SerializerInterface::class);
        $serializer2 = $this->container->get(SerializerInterface::class);
        expect($serializer1)->toBe($serializer2);
    });

    it('configures Doctrine type with serializer and type mapper', function () {
        $type = Type::getType(LazyJsonDocumentType::NAME);

        // Type should be properly configured with dependencies
        expect($type)->toBeInstanceOf(LazyJsonDocumentType::class);

        // Test that the type can perform conversions (indicates proper configuration)
        $testData = ['test' => 'value'];
        $platform = $this->entityManager->getConnection()->getDatabasePlatform();

        $databaseValue = $type->convertToDatabaseValue($testData, $platform);
        expect($databaseValue)->toBeString();

        $phpValue = $type->convertToPHPValue($databaseValue, $platform);
        expect($phpValue)->toBe($testData);
    });

    it('handles bundle boot process correctly', function () {
        // Bundle should boot without errors
        expect($this->kernel->isBooted())->toBeTrue();

        // Services should be available after boot
        expect($this->container->get(SerializerInterface::class))->not->toBeNull();
        expect($this->container->get(TypeMapperInterface::class))->not->toBeNull();
    });

    it('loads bundle configuration correctly', function () {
        // Test that configuration is loaded and applied
        $typeMapper = $this->container->get(TypeMapperInterface::class);

        // All configured type mappings should be available
        $expectedMappings = [
            'user' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\User',
            'profile' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Profile',
            'address' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Address',
            'product' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Product',
            'product_specs' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\ProductSpecifications',
            'product_variant' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\ProductVariant',
            'category' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Category',
            'order' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Order',
            'order_item' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\OrderItem',
            'order_shipping' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\OrderShipping',
            'order_payment' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\OrderPayment',
            'user_preference' => 'Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\UserPreference',
        ];

        foreach ($expectedMappings as $alias => $className) {
            expect($typeMapper->getClassByType($alias))->toBe($className);
            expect($typeMapper->getTypeByClass($className))->toBe($alias);
        }
    });

    it('integrates with Doctrine ORM correctly', function () {
        // Test that Doctrine can use the custom type
        $connection = $this->entityManager->getConnection();
        $platform = $connection->getDatabasePlatform();

        // Type should be registered and usable
        expect(Type::hasType(LazyJsonDocumentType::NAME))->toBeTrue();

        $type = Type::getType(LazyJsonDocumentType::NAME);
        $sqlDeclaration = $type->getSQLDeclaration([], $platform);

        expect($sqlDeclaration)->toBeString();
        expect($sqlDeclaration)->not->toBeEmpty();
    });

    it('provides correct service aliases', function () {
        // Test service aliases are configured correctly
        expect($this->container->has('Vuryss\\DoctrineLazyJsonOdm\\Serializer\\SerializerInterface'))->toBeTrue();
        expect($this->container->has('Vuryss\\DoctrineLazyJsonOdm\\TypeMapper\\TypeMapperInterface'))->toBeTrue();

        $serializer = $this->container->get('Vuryss\\DoctrineLazyJsonOdm\\Serializer\\SerializerInterface');
        $typeMapper = $this->container->get('Vuryss\\DoctrineLazyJsonOdm\\TypeMapper\\TypeMapperInterface');

        expect($serializer)->toBeInstanceOf(SerializerInterface::class);
        expect($typeMapper)->toBeInstanceOf(TypeMapperInterface::class);
    });

    it('handles missing configuration gracefully', function () {
        // Even with minimal configuration, bundle should work
        expect($this->container->get(SerializerInterface::class))->not->toBeNull();
        expect($this->container->get(TypeMapperInterface::class))->not->toBeNull();

        // Type mapper should handle unmapped classes
        $typeMapper = $this->container->get(TypeMapperInterface::class);
        $unmappedClass = 'Some\\Unmapped\\Class';

        expect($typeMapper->getTypeByClass($unmappedClass))->toBe($unmappedClass);
        expect($typeMapper->getClassByType('unmapped_type'))->toBe('unmapped_type');
    });

    it('supports autowiring of services', function () {
        // Services should be autowirable
        $serializer = $this->container->get(SerializerInterface::class);
        $typeMapper = $this->container->get(TypeMapperInterface::class);

        expect($serializer)->not->toBeNull();
        expect($typeMapper)->not->toBeNull();

        // Test that they work together
        $testObject = new stdClass();
        $testObject->property = 'value';

        $json = $serializer->serialize($testObject);
        $deserialized = $serializer->deserialize($json);

        expect($deserialized)->toBeInstanceOf(stdClass::class);
        expect($deserialized->property)->toBe('value');
    });

    it('maintains service container performance', function () {
        $startTime = microtime(true);

        // Access services multiple times
        for ($i = 0; $i < 100; ++$i) {
            $serializer = $this->container->get(SerializerInterface::class);
            $typeMapper = $this->container->get(TypeMapperInterface::class);
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // Service resolution should be fast
        expect($executionTime)->toBeLessThan(0.1);
    });

    it('handles bundle shutdown correctly', function () {
        // Bundle should handle shutdown gracefully
        expect($this->kernel->isBooted())->toBeTrue();

        // Services should still be accessible before shutdown
        expect($this->container->get(SerializerInterface::class))->not->toBeNull();

        // This is tested implicitly by the tearDown method
        expect(true)->toBeTrue();
    });
});
