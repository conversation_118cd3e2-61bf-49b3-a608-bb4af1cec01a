<?php

declare(strict_types=1);

use Vuryss\DoctrineLazyJsonOdm\Serializer\Serializer;
use Vuryss\DoctrineLazyJsonOdm\Serializer\SerializerInterface;
use Vuryss\DoctrineLazyJsonOdm\Tests\Fixtures\Entity\User;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapper;
use Vuryss\DoctrineLazyJsonOdm\TypeMapper\TypeMapperInterface;

describe('Service Container Setup', function () {
    it('registers all required services', function () {
        $requiredServices = [
            SerializerInterface::class,
            TypeMapperInterface::class,
            Serializer::class,
            TypeMapper::class,
        ];

        foreach ($requiredServices as $serviceId) {
            expect($this->container->has($serviceId))->toBeTrue("Service $serviceId should be registered");
            expect($this->container->get($serviceId))->not->toBeNull("Service $serviceId should be instantiable");
        }
    });

    it('provides correct service aliases', function () {
        // Interface should resolve to concrete implementation
        $serializer = $this->container->get(SerializerInterface::class);
        $concreteSerializer = $this->container->get(Serializer::class);

        expect($serializer)->toBe($concreteSerializer);

        $typeMapper = $this->container->get(TypeMapperInterface::class);
        $concreteTypeMapper = $this->container->get(TypeMapper::class);

        expect($typeMapper)->toBe($concreteTypeMapper);
    });

    it('configures services as singletons', function () {
        // Services should be singletons
        $serializer1 = $this->container->get(SerializerInterface::class);
        $serializer2 = $this->container->get(SerializerInterface::class);
        expect($serializer1)->toBe($serializer2);

        $typeMapper1 = $this->container->get(TypeMapperInterface::class);
        $typeMapper2 = $this->container->get(TypeMapperInterface::class);
        expect($typeMapper1)->toBe($typeMapper2);
    });

    it('injects dependencies correctly', function () {
        $serializer = $this->container->get(Serializer::class);
        $typeMapper = $this->container->get(TypeMapper::class);

        // Test that serializer uses the configured type mapper
        $user = new User('Test User', '<EMAIL>');
        $json = $serializer->serialize($user);
        $data = json_decode($json, true);

        // Should use alias from type mapper
        expect($data['#type'])->toBe('user');

        // Verify type mapper is working
        expect($typeMapper->getTypeByClass(User::class))->toBe('user');
    });

    it('supports autowiring for custom services', function () {
        // Create a test service that depends on our interfaces
        $testService = new class($this->container->get(SerializerInterface::class), $this->container->get(TypeMapperInterface::class)) {
            private SerializerInterface $serializer;
            private TypeMapperInterface $typeMapper;

            public function __construct(SerializerInterface $serializer, TypeMapperInterface $typeMapper)
            {
                $this->serializer = $serializer;
                $this->typeMapper = $typeMapper;
            }

            public function getSerializer(): SerializerInterface
            {
                return $this->serializer;
            }

            public function getTypeMapper(): TypeMapperInterface
            {
                return $this->typeMapper;
            }
        };

        expect($testService->getSerializer())->toBeInstanceOf(SerializerInterface::class);
        expect($testService->getTypeMapper())->toBeInstanceOf(TypeMapperInterface::class);
    });

    it('provides public services for external access', function () {
        // Services should be public and accessible
        expect($this->container->get(SerializerInterface::class))->not->toBeNull();
        expect($this->container->get(TypeMapperInterface::class))->not->toBeNull();

        // Test that they work correctly
        $serializer = $this->container->get(SerializerInterface::class);
        $typeMapper = $this->container->get(TypeMapperInterface::class);

        $user = new User('Public Test', '<EMAIL>');
        $json = $serializer->serialize($user);
        $deserialized = $serializer->deserialize($json);

        expect($deserialized)->toBeInstanceOf(User::class);
        expect($deserialized->getName())->toBe('Public Test');
    });

    it('handles service configuration parameters', function () {
        $typeMapper = $this->container->get(TypeMapper::class);

        // Type mapper should be configured with parameters from bundle config
        expect($typeMapper->getClassByType('user'))->toBe('Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\User');
        expect($typeMapper->getClassByType('profile'))->toBe('Vuryss\\DoctrineLazyJsonOdm\\Tests\\Fixtures\\Entity\\Profile');
    });

    it('supports optional service dependencies', function () {
        $serializer = $this->container->get(Serializer::class);

        // Serializer should work even without cache pool configured
        expect($serializer)->toBeInstanceOf(Serializer::class);

        $testData = ['test' => 'value'];
        $json = $serializer->serialize($testData);
        $deserialized = $serializer->deserialize($json);

        expect($deserialized)->toBe($testData);
    });

    it('maintains service performance', function () {
        $startTime = microtime(true);

        // Access services multiple times
        for ($i = 0; $i < 1000; ++$i) {
            $serializer = $this->container->get(SerializerInterface::class);
            $typeMapper = $this->container->get(TypeMapperInterface::class);
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // Service resolution should be very fast
        expect($executionTime)->toBeLessThan(0.1);
    });

    it('handles service instantiation errors gracefully', function () {
        // All configured services should instantiate without errors
        $services = [
            SerializerInterface::class,
            TypeMapperInterface::class,
            Serializer::class,
            TypeMapper::class,
        ];

        foreach ($services as $serviceId) {
            expect(fn () => $this->container->get($serviceId))->not->toThrow();
        }
    });

    it('provides correct service metadata', function () {
        // Test service definitions
        expect($this->container->has(SerializerInterface::class))->toBeTrue();
        expect($this->container->has(TypeMapperInterface::class))->toBeTrue();

        // Services should be properly typed
        $serializer = $this->container->get(SerializerInterface::class);
        $typeMapper = $this->container->get(TypeMapperInterface::class);

        expect($serializer)->toBeInstanceOf(SerializerInterface::class);
        expect($typeMapper)->toBeInstanceOf(TypeMapperInterface::class);
    });

    it('supports service decoration', function () {
        // Test that services can be decorated if needed
        $originalSerializer = $this->container->get(SerializerInterface::class);

        expect($originalSerializer)->toBeInstanceOf(Serializer::class);

        // Service should work correctly
        $testData = 'test';
        $json = $originalSerializer->serialize($testData);
        expect($json)->toBe('"test"');
    });

    it('handles circular dependency prevention', function () {
        // Services should not have circular dependencies
        $serializer = $this->container->get(SerializerInterface::class);
        $typeMapper = $this->container->get(TypeMapperInterface::class);

        expect($serializer)->not->toBeNull();
        expect($typeMapper)->not->toBeNull();

        // Both should work independently
        expect($typeMapper->getTypeByClass('SomeClass'))->toBe('SomeClass');
        expect($serializer->serialize('test'))->toBe('"test"');
    });

    it('provides consistent service behavior', function () {
        // Services should behave consistently across multiple calls
        $serializer = $this->container->get(SerializerInterface::class);

        $testData = new User('Consistent Test', '<EMAIL>');

        $json1 = $serializer->serialize($testData);
        $json2 = $serializer->serialize($testData);

        expect($json1)->toBe($json2);

        $deserialized1 = $serializer->deserialize($json1);
        $deserialized2 = $serializer->deserialize($json2);

        expect($deserialized1->getName())->toBe($deserialized2->getName());
        expect($deserialized1->getEmail())->toBe($deserialized2->getEmail());
    });

    it('supports service compilation optimization', function () {
        // Container should be optimized for production
        expect($this->container)->not->toBeNull();

        // Services should be accessible
        $serializer = $this->container->get(SerializerInterface::class);
        $typeMapper = $this->container->get(TypeMapperInterface::class);

        expect($serializer)->toBeInstanceOf(SerializerInterface::class);
        expect($typeMapper)->toBeInstanceOf(TypeMapperInterface::class);
    });

    it('handles service scope correctly', function () {
        // Services should have correct scope (singleton by default)
        $serializer1 = $this->container->get(SerializerInterface::class);
        $serializer2 = $this->container->get(SerializerInterface::class);

        expect($serializer1)->toBe($serializer2);

        // Test with different interface access
        $serializer3 = $this->container->get(Serializer::class);
        expect($serializer1)->toBe($serializer3);
    });
});
